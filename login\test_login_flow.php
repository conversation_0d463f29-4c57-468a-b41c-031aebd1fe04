<?php
// Test script to create a test user and verify login flow
require_once '../dbconnect/_dbconnect.php';

echo "<h2>First-Time Login Flow Test</h2>";

try {
    $pdo = db_connect();
    
    // Create a test user
    $testUsername = 'testuser_' . time();
    $testEmail = $testUsername . '@test.com';
    $testName = 'Test User';
    $defaultPassword = 'pwd123';
    
    echo "<h3>Step 1: Creating Test User</h3>";
    
    // Hash the default password
    $hashedPassword = password_hash($defaultPassword, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3,
    ]);
    
    // Insert test user with first_login = 1
    $stmt = $pdo->prepare("INSERT INTO kp_login (user, pass, email, name, role, status, first_login, created_at) VALUES (?, ?, ?, ?, 'user', 1, 1, NOW())");
    $result = $stmt->execute([$testUsername, $hashedPassword, $testEmail, $testName]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ Test user created successfully!</p>";
        echo "<p><strong>Username:</strong> <code>$testUsername</code></p>";
        echo "<p><strong>Default Password:</strong> <code>$defaultPassword</code></p>";
        echo "<p><strong>User ID:</strong> $userId</p>";
        
        // Test the check_login function
        echo "<h3>Step 2: Testing Login Function</h3>";
        
        $loginResult = check_login($testUsername, $defaultPassword);
        echo "<p><strong>Login Result:</strong> <code>$loginResult</code></p>";
        
        if ($loginResult === 'first_login_required') {
            echo "<p style='color: green;'>✅ First-time login detection working correctly!</p>";
            echo "<p>User should be redirected to change password page.</p>";
        } elseif ($loginResult === 'success') {
            echo "<p style='color: orange;'>⚠️ Login successful but first-time login not detected.</p>";
        } else {
            echo "<p style='color: red;'>❌ Login failed: $loginResult</p>";
        }
        
        // Check session variables
        echo "<h3>Step 3: Session Variables Check</h3>";
        if (isset($_SESSION['first_login'])) {
            echo "<p><strong>first_login session:</strong> " . $_SESSION['first_login'] . "</p>";
            if ($_SESSION['first_login'] == 1) {
                echo "<p style='color: green;'>✅ Session correctly set for first-time login</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Session first_login is not 1</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ first_login session variable not set</p>";
        }
        
        // Test password change simulation
        echo "<h3>Step 4: Password Change Simulation</h3>";
        $newPassword = 'newpassword123!';
        $newHashedPassword = password_hash($newPassword, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3,
        ]);
        
        $updateStmt = $pdo->prepare("UPDATE kp_login SET pass = ?, first_login = 0 WHERE id = ?");
        $updateResult = $updateStmt->execute([$newHashedPassword, $userId]);
        
        if ($updateResult) {
            echo "<p style='color: green;'>✅ Password updated successfully</p>";
            
            // Test login with new password
            $secondLoginResult = check_login($testUsername, $newPassword);
            echo "<p><strong>Second Login Result:</strong> <code>$secondLoginResult</code></p>";
            
            if ($secondLoginResult === 'success') {
                echo "<p style='color: green;'>✅ Normal login working after password change</p>";
            } else {
                echo "<p style='color: red;'>❌ Login failed after password change: $secondLoginResult</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to update password</p>";
        }
        
        // Clean up - delete test user
        echo "<h3>Step 5: Cleanup</h3>";
        $deleteStmt = $pdo->prepare("DELETE FROM kp_login WHERE id = ?");
        $deleteResult = $deleteStmt->execute([$userId]);
        
        if ($deleteResult) {
            echo "<p style='color: green;'>✅ Test user deleted successfully</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Failed to delete test user (ID: $userId)</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test user</p>";
    }
    
    echo "<h3>Test Summary</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Manual Testing Steps:</h4>";
    echo "<ol>";
    echo "<li>Go to <a href='../userManage/' target='_blank'>User Management</a> and add a new user</li>";
    echo "<li>Note the default password shown in the success message</li>";
    echo "<li>Go to <a href='index.php' target='_blank'>Login Page</a></li>";
    echo "<li>Login with the new username and default password 'pwd123'</li>";
    echo "<li>Should be redirected to <a href='change_password.php' target='_blank'>Change Password Page</a></li>";
    echo "<li>Change the password and verify redirect to dashboard</li>";
    echo "<li>Logout and login again with new password (should go directly to dashboard)</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
    pre { background-color: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>
