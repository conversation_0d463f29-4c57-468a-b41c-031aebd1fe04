<?php
/**
 * User Management Actions
 * 
 * Handles user CRUD operations
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/User.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Check if user has admin role
if (Session::get('role') !== 'admin') {
    header('Location: index.php?error=unauthorized');
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit;
}

// Validate CSRF token
$token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
if (!Session::validateCsrfToken($token)) {
    header('Location: index.php?error=invalid_token');
    exit;
}

// Get action
$action = isset($_POST['action']) ? Security::sanitizeInput($_POST['action']) : '';

// Create User model instance
$userModel = new User();

// Process action
switch ($action) {
    case 'add':
        // Add new user
        handleAddUser($userModel);
        break;
    
    case 'edit':
        // Edit existing user
        handleEditUser($userModel);
        break;
    
    case 'delete':
        // Delete user
        handleDeleteUser($userModel);
        break;
    
    default:
        // Invalid action
        header('Location: index.php?error=invalid_action');
        exit;
}

/**
 * Handle adding a new user
 * 
 * @param User $userModel User model instance
 */
function handleAddUser($userModel) {
    // Validate and sanitize inputs
    $username = isset($_POST['username']) ? Security::sanitizeInput($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : ''; // Don't sanitize password
    $name = isset($_POST['name']) ? Security::sanitizeInput($_POST['name']) : '';
    $email = isset($_POST['email']) ? Security::sanitizeInput($_POST['email']) : '';
    $role = isset($_POST['role']) ? Security::sanitizeInput($_POST['role']) : 'user';
    $status = isset($_POST['status']) ? (int)$_POST['status'] : 1;
    
    // Validate required fields
    if (empty($username) || empty($password) || empty($name) || empty($email)) {
        header('Location: index.php?error=add&message=missing_fields');
        exit;
    }
    
    // Validate email
    if (!Security::validateEmail($email)) {
        header('Location: index.php?error=add&message=invalid_email');
        exit;
    }
    
    // Validate role
    if (!in_array($role, ['super_admin', 'admin', 'user'])) {
        $role = 'user'; // Default to user role
    }
    
    // Validate status
    if ($status !== 0 && $status !== 1) {
        $status = 1; // Default to active
    }
    
    // Check if username already exists
    $existingUser = $userModel->getUserByUsername($username);
    if ($existingUser) {
        header('Location: index.php?error=add&message=username_exists');
        exit;
    }
    
    // Prepare user data
    $userData = [
        'user' => $username,
        'pass' => $password,
        'name' => $name,
        'email' => $email,
        'role' => $role,
        'status' => $status
    ];
    
    // Create user
    $result = $userModel->createUser($userData);
    
    if ($result) {
        // Log the action
        error_log("User {$_SESSION['username']} created new user: {$username}");
        
        // Redirect to success page
        header('Location: index.php?success=add');
    } else {
        // Redirect to error page
        header('Location: index.php?error=add');
    }
    exit;
}

/**
 * Handle editing an existing user
 * 
 * @param User $userModel User model instance
 */
function handleEditUser($userModel) {
    // Validate and sanitize inputs
    $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
    $username = isset($_POST['username']) ? Security::sanitizeInput($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : ''; // Don't sanitize password
    $name = isset($_POST['name']) ? Security::sanitizeInput($_POST['name']) : '';
    $email = isset($_POST['email']) ? Security::sanitizeInput($_POST['email']) : '';
    $role = isset($_POST['role']) ? Security::sanitizeInput($_POST['role']) : 'user';
    $status = isset($_POST['status']) ? (int)$_POST['status'] : 1;
    
    // Validate required fields
    if ($userId <= 0 || empty($username) || empty($name) || empty($email)) {
        header('Location: index.php?error=edit&message=missing_fields');
        exit;
    }
    
    // Validate email
    if (!Security::validateEmail($email)) {
        header('Location: index.php?error=edit&message=invalid_email');
        exit;
    }
    
    // Validate role
    if (!in_array($role, ['super_admin', 'admin', 'user'])) {
        $role = 'user'; // Default to user role
    }
    
    // Validate status
    if ($status !== 0 && $status !== 1) {
        $status = 1; // Default to active
    }
    
    // Get existing user
    $existingUser = $userModel->getUserById($userId);
    if (!$existingUser) {
        header('Location: index.php?error=edit&message=user_not_found');
        exit;
    }
    
    // Prepare user data
    $userData = [
        'name' => $name,
        'email' => $email,
        'role' => $role,
        'status' => $status
    ];
    
    // Add password to user data if provided
    if (!empty($password)) {
        $userData['pass'] = $password;
    }
    
    // Update user
    $result = $userModel->updateUser($userId, $userData);
    
    if ($result) {
        // Log the action
        error_log("User {$_SESSION['username']} updated user: {$username}");
        
        // Redirect to success page
        header('Location: index.php?success=edit');
    } else {
        // Redirect to error page
        header('Location: index.php?error=edit');
    }
    exit;
}

/**
 * Handle deleting a user
 * 
 * @param User $userModel User model instance
 */
function handleDeleteUser($userModel) {
    // Validate and sanitize inputs
    $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
    
    // Validate user ID
    if ($userId <= 0) {
        header('Location: index.php?error=delete&message=invalid_id');
        exit;
    }
    
    // Prevent self-deletion
    if ($userId == Session::get('id')) {
        header('Location: index.php?error=delete&message=cannot_delete_self');
        exit;
    }
    
    // Get existing user
    $existingUser = $userModel->getUserById($userId);
    if (!$existingUser) {
        header('Location: index.php?error=delete&message=user_not_found');
        exit;
    }
    
    // Delete user
    $result = $userModel->deleteUser($userId);
    
    if ($result) {
        // Log the action
        error_log("User {$_SESSION['username']} deleted user: {$existingUser['user']}");
        
        // Redirect to success page
        header('Location: index.php?success=delete');
    } else {
        // Redirect to error page
        header('Location: index.php?error=delete');
    }
    exit;
}
