<?php
/**
 * User Model
 *
 * Handles user authentication and management
 */
require_once dirname(__DIR__) . '/models/Database.php';

class User {
    private $db;

    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Authenticate a user
     *
     * @param string $username Username
     * @param string $password Password
     * @return array|bool User data if authenticated, false otherwise
     */
    public function authenticate($username, $password) {
        try {
            // Get user data
            $sql = "SELECT * FROM kp_login WHERE user = ?";
            $user = $this->db->getRow($sql, [$username]);

            // Check if user exists
            if (!$user) {
                error_log("User not found: {$username}");
                return false;
            }

            // Check if account is active
            if ($user['status'] != 1) {
                error_log("Account inactive for user: {$username}");
                return false;
            }

            $authenticated = false;

            // Check if the password is hashed
            $password_info = password_get_info($user['pass']);

            // First try plain text comparison (for legacy passwords)
            if ($user['pass'] === $password) {
                $authenticated = true;

                // Upgrade to hashed password
                $this->updatePasswordHash($user['id'], $password);
            }
            // If it looks like a hash, try password_verify
            else if ($password_info['algo'] !== 0) {
                $authenticated = password_verify($password, $user['pass']);

                // Check if the password hash needs to be rehashed
                if ($authenticated && password_needs_rehash($user['pass'], PASSWORD_ARGON2ID)) {
                    $this->updatePasswordHash($user['id'], $password);
                }
            }

            if ($authenticated) {
                return $user;
            }

            // Log failed login attempt
            $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Unknown';
            error_log("Failed login attempt: User {$username} from IP {$ip}");

            return false;
        } catch (Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a user's password hash
     *
     * @param int $userId User ID
     * @param string $plainPassword Plain text password
     * @return bool True if successful, false otherwise
     */
    public function updatePasswordHash($userId, $plainPassword) {
        try {
            // Hash the password using Argon2id
            $hashedPassword = password_hash($plainPassword, PASSWORD_ARGON2ID, [
                'memory_cost' => 65536, // 64MB
                'time_cost' => 4,       // 4 iterations
                'threads' => 3          // 3 threads
            ]);

            // Update the password in the database
            $data = ['pass' => $hashedPassword];
            $this->db->update('kp_login', $data, 'id = ?', [$userId]);

            return true;
        } catch (Exception $e) {
            error_log("Failed to update password hash: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new user
     *
     * @param array $userData User data
     * @return int|bool User ID if created, false otherwise
     */
    public function createUser($userData) {
        try {
            // Hash the password
            $userData['pass'] = password_hash($userData['pass'], PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3
            ]);

            // Generate a unique user key if not provided
            if (!isset($userData['user_key']) || empty($userData['user_key'])) {
                $userData['user_key'] = $this->generateUserKey();
            }

            // Insert the user
            return $this->db->insert('kp_login', $userData);
        } catch (Exception $e) {
            error_log("Failed to create user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a user
     *
     * @param int $userId User ID
     * @param array $userData User data
     * @return bool True if successful, false otherwise
     */
    public function updateUser($userId, $userData) {
        try {
            // If password is being updated, hash it
            if (isset($userData['pass']) && !empty($userData['pass'])) {
                $userData['pass'] = password_hash($userData['pass'], PASSWORD_ARGON2ID, [
                    'memory_cost' => 65536,
                    'time_cost' => 4,
                    'threads' => 3
                ]);
            } else {
                // Don't update password if not provided
                unset($userData['pass']);
            }

            // Update the user
            $this->db->update('kp_login', $userData, 'id = ?', [$userId]);

            return true;
        } catch (Exception $e) {
            error_log("Failed to update user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a user by ID
     *
     * @param int $userId User ID
     * @return array|bool User data if found, false otherwise
     */
    public function getUserById($userId) {
        try {
            $sql = "SELECT * FROM kp_login WHERE id = ?";
            return $this->db->getRow($sql, [$userId]);
        } catch (Exception $e) {
            error_log("Failed to get user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a user by username
     *
     * @param string $username Username
     * @return array|bool User data if found, false otherwise
     */
    public function getUserByUsername($username) {
        try {
            $sql = "SELECT * FROM kp_login WHERE user = ?";
            return $this->db->getRow($sql, [$username]);
        } catch (Exception $e) {
            error_log("Failed to get user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all users
     *
     * @return array|bool Users data if found, false otherwise
     */
    public function getAllUsers() {
        try {
            $sql = "SELECT id, user, name, email, role, status, user_key, create_date, update_date FROM kp_login";
            return $this->db->getRows($sql);
        } catch (Exception $e) {
            error_log("Failed to get users: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a user
     *
     * @param int $userId User ID
     * @return bool True if successful, false otherwise
     */
    public function deleteUser($userId) {
        try {
            return $this->db->delete('kp_login', 'id = ?', [$userId]);
        } catch (Exception $e) {
            error_log("Failed to delete user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate a unique user key
     *
     * @return string User key
     */
    private function generateUserKey() {
        return bin2hex(random_bytes(16));
    }
}
