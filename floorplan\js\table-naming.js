/**
 * Table Naming Conversion Script
 * This script updates the table naming in the floorplan to the new format:
 * - Floor 1: A1/1, A1/2, ... and A2/1, A2/2, ... (formerly 1A01, 1B01, etc.)
 * - Floor 2: 2A01, 2A02, ... and 2B01, 2B02, ... and 2H01, 2H02, ... (front zone)
 * - Floor 3: 3A01, 3A02, ... and 3B01, 3B02, ... and 3C01, 3C02, ...
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait for SVG elements to be fully loaded
    setTimeout(updateTableNames, 500);
});

function updateTableNames() {
    // Get all SVG elements in the document
    const svgElements = document.querySelectorAll('svg');

    svgElements.forEach((svg, index) => {
        // Determine which floor this SVG represents based on its position or parent
        let floorNumber = 1; // Default to floor 1

        // Check if this is Floor 2 or Floor 3 based on the image source
        const imageElement = svg.querySelector('image');
        if (imageElement) {
            const imageSrc = imageElement.getAttribute('href');
            if (imageSrc && imageSrc.includes('Floor_Plan_2')) {
                floorNumber = 2;
            } else if (imageSrc && imageSrc.includes('Floor_Plan_3')) {
                floorNumber = 3;
            }
        }

        // Update table names based on floor number
        updateFloorTables(svg, floorNumber);
    });

    // Update any data attributes or other references to tables
    updateTableReferences();
}

function updateFloorTables(svg, floorNumber) {
    // Get all table elements in this SVG
    const tableElements = svg.querySelectorAll('a[id^="box-"]');

    tableElements.forEach(table => {
        // Get the current table ID and text
        const tableId = table.getAttribute('id');
        const textElement = table.querySelector('text');
        const rectElement = table.querySelector('rect');

        if (!textElement) return; // Skip if no text element

        const currentText = textElement.textContent.trim();
        let newText = '';
        let newId = '';

        // Skip VIP rooms
        if (tableId.includes('vip') || currentText.includes('VIP')) {
            return;
        }

        // Convert table name based on floor and current name
        /*
        if (floorNumber === 1) {
            // Floor 1: A1->A1/1, B1->A2/1, etc.
            if (currentText.startsWith('A')) {
                const number = currentText.substring(1);
                newText = `A1/${number}`;
                newId = `box-A1/${number}`;
            } else if (currentText.startsWith('B')) {
                const number = currentText.substring(1);
                newText = `A2/${number}`;
                newId = `box-A2/${number}`;
            } else if (currentText.startsWith('1A')) {
                // Convert from previous format 1A01 to A1/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A1/${number}`;
                newId = `box-A1/${number}`;
            } else if (currentText.startsWith('1B')) {
                // Convert from previous format 1B01 to A2/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A2/${number}`;
                newId = `box-A2/${number}`;
            } else if (currentText.startsWith('1C')) {
                // Convert from previous format 1C01 to A3/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A3/${number}`;
                newId = `box-A3/${number}`;
            } else if (currentText.startsWith('1D')) {
                // Convert from previous format 1D01 to A4/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A4/${number}`;
                newId = `box-A4/${number}`;
            } else if (currentText.startsWith('1E')) {
                // Convert from previous format 1E01 to A5/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A5/${number}`;
                newId = `box-A5/${number}`;
            } else if (currentText.startsWith('1F')) {
                // Convert from previous format 1F01 to A6/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A6/${number}`;
                newId = `box-A6/${number}`;
            } else if (currentText.startsWith('1G')) {
                // Convert from previous format 1G01 to A7/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A7/${number}`;
                newId = `box-A7/${number}`;
            } else if (currentText.startsWith('1H')) {
                // Convert from previous format 1H01 to A8/1
                const number = parseInt(currentText.substring(2), 10);
                newText = `A8/${number}`;
                newId = `box-A8/${number}`;
            }
        } else if (floorNumber === 2) {
            // Floor 2: B1->2A01, C1->2B01, D1->2C01, etc.
            if (currentText.startsWith('B')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `2A${number}`;
                newId = `box-2A${number}`;
            } else if (currentText.startsWith('C')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `2B${number}`;
                newId = `box-2B${number}`;
            } else if (currentText.startsWith('D')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `2C${number}`;
                newId = `box-2C${number}`;
            }

            // Add front zone tables (2H01, 2H02, etc.) if needed
            // This would require adding new elements to the SVG
        } else if (floorNumber === 3) {
            // Floor 3: E1->3A01, F1->3B01, G1->3C01, etc.
            if (currentText.startsWith('E')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `3A${number}`;
                newId = `box-3A${number}`;
            } else if (currentText.startsWith('F')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `3B${number}`;
                newId = `box-3B${number}`;
            } else if (currentText.startsWith('G')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `3C${number}`;
                newId = `box-3C${number}`;
            } else if (currentText.startsWith('P')) {
                const number = currentText.substring(1).padStart(2, '0');
                newText = `3P${number}`;
                newId = `box-3P${number}`;
            }
        }
        */

        // Update the text and ID if we have new values
        if (newText && newId) {
            textElement.textContent = newText;
            table.setAttribute('id', newId);

            // Update the rect ID if it exists
            if (rectElement && rectElement.getAttribute('id')) {
                rectElement.setAttribute('id', 'r' + newId);
            }
        }
    });
}

function updateTableReferences() {
    // Update any data attributes or event handlers that reference table IDs
    // This would depend on how tables are referenced in the application

    // Example: Update click handlers
    document.querySelectorAll('a[id^="box-"]').forEach(table => {
        // Remove old event listeners and add new ones if needed
        // This is just a placeholder - actual implementation would depend on the application
    });
}
