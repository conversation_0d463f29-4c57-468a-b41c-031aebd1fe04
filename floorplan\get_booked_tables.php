<?php
// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Get parameters with defaults
    $useDate = $_GET['useDate'] ?? date('Y-m-d');
    $useZone = $_GET['useZone'] ?? '1';

    // Ensure useZone is not empty
    if (empty($useZone)) {
        $useZone = '1';
    }

    // Get bookings from database
    $getBookings = get_browser_bookings($useDate, $useZone);
    $bookedTables = [];

    // Process bookings
    foreach ($getBookings as $booking) {
        if (!empty($booking['tables'])) {
            $tables = explode(',', $booking['tables']);
            foreach ($tables as $table) {
                if (!empty(trim($table))) {
                    $bookedTables[] = trim($table);
                }
            }
        }
    }

    // Remove duplicates
    $bookedTables = array_unique($bookedTables);

    // Return booked tables as JSON
    // Make sure we're returning a JSON array, not just a string
    echo json_encode(array_values($bookedTables));

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
