-- Drop the table if it exists
DROP TABLE IF EXISTS `kp_login`;
-- Create the login table
CREATE TABLE `kp_login` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user` VARCHAR(50) NOT NULL,
  `pass` VA<PERSON><PERSON><PERSON>(255) NOT NULL COMMENT 'Stores password hash (Argon2id)',
  `name` VARCHAR(100) DEFAULT NULL,
  `email` VARCHAR(100) DEFAULT NULL,
  `role` ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user',
  `status` TINYINT(1) NOT NULL DEFAULT 1,
  `user_key` VARCHAR(50) DEFAULT NULL,
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user` (`user`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;