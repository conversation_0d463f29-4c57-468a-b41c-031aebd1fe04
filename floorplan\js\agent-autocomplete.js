/**
 * Agent Autocomplete Functionality
 * Provides enhanced autocomplete for agent fields
 */

$(document).ready(function() {
    // Define the list of agents
    const agentList = [
        "Agent 1",
        "Agent 2",
        "Agent 3",
        "Agent 4",
        "Agent 5",
        "Bangkok Tour",
        "Phuket Tour",
        "Chiang Mai Tour",
        "Pattaya Tour",
        "Krabi Tour"
    ];

    // Function to initialize autocomplete on agent fields
    function initAgentAutocomplete() {
        console.log('Initializing agent autocomplete');
        
        // Clear existing options
        $('#agent-list, #buyout-agent-list').empty();
        
        // Add options to datalists
        agentList.forEach(function(agent) {
            $('#agent-list, #buyout-agent-list').append(`<option value="${agent}">`);
        });
        
        // Add custom styling for datalist inputs
        $('.agent-autocomplete').css({
            'background-color': '#f8f9fa',
            'border-color': '#ced4da'
        });
        
        // Add focus effect
        $('.agent-autocomplete').on('focus', function() {
            $(this).css({
                'background-color': '#fff',
                'border-color': '#80bdff',
                'box-shadow': '0 0 0 0.2rem rgba(0, 123, 255, 0.25)'
            });
        });
        
        // Remove focus effect
        $('.agent-autocomplete').on('blur', function() {
            $(this).css({
                'background-color': '#f8f9fa',
                'border-color': '#ced4da',
                'box-shadow': 'none'
            });
        });
        
        // Add input event to handle custom filtering
        $('.agent-autocomplete').on('input', function() {
            const input = $(this).val().toLowerCase();
            
            // If the input is empty, show all options
            if (input === '') {
                return;
            }
            
            // Filter the agent list based on input
            const filteredAgents = agentList.filter(agent => 
                agent.toLowerCase().includes(input)
            );
            
            // Update the corresponding datalist
            const datalistId = $(this).attr('list');
            $(`#${datalistId}`).empty();
            
            filteredAgents.forEach(agent => {
                $(`#${datalistId}`).append(`<option value="${agent}">`);
            });
        });
    }
    
    // Initialize autocomplete when the document is ready
    initAgentAutocomplete();
    
    // Re-initialize when modals are shown
    $('#bookingModal, #buyoutFloorModal').on('shown.bs.modal', function() {
        initAgentAutocomplete();
    });
});
