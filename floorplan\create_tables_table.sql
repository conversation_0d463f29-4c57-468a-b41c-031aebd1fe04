-- Drop the table if it exists
DROP TABLE IF EXISTS `kp_tables`;
-- Create the kp_tables table
CREATE TABLE `kp_tables` (
  `table_id` VARCHAR(10) NOT NULL,
  `x_position` INT NOT NULL,
  `y_position` INT NOT NULL,
  `width` INT NOT NULL,
  `height` INT NOT NULL,
  `status` ENUM('active', 'disabled') NOT NULL DEFAULT 'active',
  `disabled_until` DATE DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;
-- Insert initial data for tables A1/1-A1/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A1/1', 383, 76, 54, 36, 'active'),
  ('A1/2', 440, 76, 54, 36, 'active'),
  ('A1/3', 497, 76, 54, 36, 'active'),
  ('A1/4', 554, 76, 54, 36, 'active'),
  ('A1/5', 611, 76, 54, 36, 'active'),
  ('A1/6', 668, 76, 54, 36, 'active'),
  ('A1/7', 725, 76, 54, 36, 'active'),
  ('A1/8', 782, 76, 54, 36, 'active'),
  ('A1/9', 839, 76, 54, 36, 'active'),
  ('A1/10', 896, 76, 54, 36, 'active'),
  ('A1/11', 953, 76, 54, 36, 'active'),
  ('A1/12', 1010, 76, 54, 36, 'active'),
  ('A1/13', 1067, 76, 54, 36, 'active'),
  ('A1/14', 1124, 76, 54, 36, 'active'),
  ('A1/15', 1181, 76, 54, 36, 'active');
-- Insert initial data for tables A2/1-A2/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A2/1', 383, 130, 54, 36, 'active'),
  ('A2/2', 440, 130, 54, 36, 'active'),
  ('A2/3', 497, 130, 54, 36, 'active'),
  ('A2/4', 554, 130, 54, 36, 'active'),
  ('A2/5', 611, 130, 54, 36, 'active'),
  ('A2/6', 668, 130, 54, 36, 'active'),
  ('A2/7', 725, 130, 54, 36, 'active'),
  ('A2/8', 782, 130, 54, 36, 'active'),
  ('A2/9', 839, 130, 54, 36, 'active'),
  ('A2/10', 896, 130, 54, 36, 'active'),
  ('A2/11', 953, 130, 54, 36, 'active'),
  ('A2/12', 1010, 130, 54, 36, 'active'),
  ('A2/13', 1067, 130, 54, 36, 'active'),
  ('A2/14', 1124, 130, 54, 36, 'active'),
  ('A2/15', 1181, 130, 54, 36, 'active');
-- Insert initial data for tables A3/1-A3/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A3/1', 383, 184, 54, 36, 'active'),
  ('A3/2', 440, 184, 54, 36, 'active'),
  ('A3/3', 497, 184, 54, 36, 'active'),
  ('A3/4', 554, 184, 54, 36, 'active'),
  ('A3/5', 611, 184, 54, 36, 'active'),
  ('A3/6', 668, 184, 54, 36, 'active'),
  ('A3/7', 725, 184, 54, 36, 'active'),
  ('A3/8', 782, 184, 54, 36, 'active'),
  ('A3/9', 839, 184, 54, 36, 'active'),
  ('A3/10', 896, 184, 54, 36, 'active'),
  ('A3/11', 953, 184, 54, 36, 'active'),
  ('A3/12', 1010, 184, 54, 36, 'active'),
  ('A3/13', 1067, 184, 54, 36, 'active'),
  ('A3/14', 1124, 184, 54, 36, 'active'),
  ('A3/15', 1181, 184, 54, 36, 'active');
-- Insert initial data for tables A4/1-A4/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A4/1', 383, 238, 54, 36, 'active'),
  ('A4/2', 440, 238, 54, 36, 'active'),
  ('A4/3', 497, 238, 54, 36, 'active'),
  ('A4/4', 554, 238, 54, 36, 'active'),
  ('A4/5', 611, 238, 54, 36, 'active'),
  ('A4/6', 668, 238, 54, 36, 'active'),
  ('A4/7', 725, 238, 54, 36, 'active'),
  ('A4/8', 782, 238, 54, 36, 'active'),
  ('A4/9', 839, 238, 54, 36, 'active'),
  ('A4/10', 896, 238, 54, 36, 'active'),
  ('A4/11', 953, 238, 54, 36, 'active'),
  ('A4/12', 1010, 238, 54, 36, 'active'),
  ('A4/13', 1067, 238, 54, 36, 'active'),
  ('A4/14', 1124, 238, 54, 36, 'active'),
  ('A4/15', 1181, 238, 54, 36, 'active');
-- Insert initial data for tables A5/1-A5/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A5/1', 383, 292, 54, 36, 'active'),
  ('A5/2', 440, 292, 54, 36, 'active'),
  ('A5/3', 497, 292, 54, 36, 'active'),
  ('A5/4', 554, 292, 54, 36, 'active'),
  ('A5/5', 611, 292, 54, 36, 'active'),
  ('A5/6', 668, 292, 54, 36, 'active'),
  ('A5/7', 725, 292, 54, 36, 'active'),
  ('A5/8', 782, 292, 54, 36, 'active'),
  ('A5/9', 839, 292, 54, 36, 'active'),
  ('A5/10', 896, 292, 54, 36, 'active'),
  ('A5/11', 953, 292, 54, 36, 'active'),
  ('A5/12', 1010, 292, 54, 36, 'active'),
  ('A5/13', 1067, 292, 54, 36, 'active'),
  ('A5/14', 1124, 292, 54, 36, 'active'),
  ('A5/15', 1181, 292, 54, 36, 'active');
-- Insert initial data for tables A6/1-A6/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A6/1', 383, 346, 54, 36, 'active'),
  ('A6/2', 440, 346, 54, 36, 'active'),
  ('A6/3', 497, 346, 54, 36, 'active'),
  ('A6/4', 554, 346, 54, 36, 'active'),
  ('A6/5', 611, 346, 54, 36, 'active'),
  ('A6/6', 668, 346, 54, 36, 'active'),
  ('A6/7', 725, 346, 54, 36, 'active'),
  ('A6/8', 782, 346, 54, 36, 'active'),
  ('A6/9', 839, 346, 54, 36, 'active'),
  ('A6/10', 896, 346, 54, 36, 'active'),
  ('A6/11', 953, 346, 54, 36, 'active'),
  ('A6/12', 1010, 346, 54, 36, 'active'),
  ('A6/13', 1067, 346, 54, 36, 'active'),
  ('A6/14', 1124, 346, 54, 36, 'active'),
  ('A6/15', 1181, 346, 54, 36, 'active');
-- Insert initial data for tables A7/1-A7/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A7/1', 383, 400, 54, 36, 'active'),
  ('A7/2', 440, 400, 54, 36, 'active'),
  ('A7/3', 497, 400, 54, 36, 'active'),
  ('A7/4', 554, 400, 54, 36, 'active'),
  ('A7/5', 611, 400, 54, 36, 'active'),
  ('A7/6', 668, 400, 54, 36, 'active'),
  ('A7/7', 725, 400, 54, 36, 'active'),
  ('A7/8', 782, 400, 54, 36, 'active'),
  ('A7/9', 839, 400, 54, 36, 'active'),
  ('A7/10', 896, 400, 54, 36, 'active'),
  ('A7/11', 953, 400, 54, 36, 'active'),
  ('A7/12', 1010, 400, 54, 36, 'active'),
  ('A7/13', 1067, 400, 54, 36, 'active'),
  ('A7/14', 1124, 400, 54, 36, 'active'),
  ('A7/15', 1181, 400, 54, 36, 'active');
-- Insert initial data for tables A8/1-A8/15
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES ('A8/1', 383, 454, 54, 36, 'active'),
  ('A8/2', 440, 454, 54, 36, 'active'),
  ('A8/3', 497, 454, 54, 36, 'active'),
  ('A8/4', 554, 454, 54, 36, 'active'),
  ('A8/5', 611, 454, 54, 36, 'active'),
  ('A8/6', 668, 454, 54, 36, 'active'),
  ('A8/7', 725, 454, 54, 36, 'active'),
  ('A8/8', 782, 454, 54, 36, 'active'),
  ('A8/9', 839, 454, 54, 36, 'active'),
  ('A8/10', 896, 454, 54, 36, 'active'),
  ('A8/11', 953, 454, 54, 36, 'active'),
  ('A8/12', 1010, 454, 54, 36, 'active'),
  ('A8/13', 1067, 454, 54, 36, 'active'),
  ('A8/14', 1124, 454, 54, 36, 'active'),
  ('A8/15', 1181, 454, 54, 36, 'active');