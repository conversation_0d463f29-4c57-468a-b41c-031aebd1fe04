-- Add payment_type column to kp_booking table if it doesn't exist
-- This script is safe to run multiple times

-- Check and add payment_type column
SET @exists_payment_type = 0;
SELECT COUNT(*) INTO @exists_payment_type
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'payment_type';

SET @sql_payment_type = IF(
        @exists_payment_type = 0,
        'ALTER TABLE kp_booking ADD COLUMN payment_type VARCHAR(20) DEFAULT "Transfer" COMMENT "Payment type: Cash, Credit Card, Transfer"',
        'SELECT "Column payment_type already exists" as message'
    );

PREPARE stmt
FROM @sql_payment_type;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
