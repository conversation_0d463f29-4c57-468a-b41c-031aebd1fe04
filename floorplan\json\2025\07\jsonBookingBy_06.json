[{"Order No": "SWD2025070003", "BookID": 102, "ZoneID": 1, "Use Date": "2025-07-06", "Amount": "100", "Create Date": "2025-07-06 01:30:48", "Customer": "MrKP", "Phone": "0865395090", "Guests": {"Adults": 4, "Children": 0, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "ABC", "Agent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaymentType": "Cash", "PaymentStatus": "Paid", "Request": "Top", "Special": 2, "Floor": "1", "Table": "C12,C12-1"}, {"Order No": "SWD2025070005", "BookID": 106, "ZoneID": 1, "Use Date": "2025-07-06", "Amount": "11111", "Create Date": "2025-07-06 15:11:56", "Customer": "Anuwat1", "Phone": "11111", "Guests": {"Adults": 1, "Children": 1, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "111", "Agent": "7Day", "PaymentType": "Transfer", "PaymentStatus": "WP", "Request": "1111", "Special": 0, "Floor": "1", "Table": "C12-3"}, {"Order No": "SWD2025070006", "BookID": 108, "ZoneID": 2, "Use Date": "2025-07-06", "Amount": "1111", "Create Date": "2025-07-06 16:49:06", "Customer": "MrKP1", "Phone": "0865395090", "Guests": {"Adults": 3, "Children": 1, "Infants": 1}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "1111", "Agent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PaymentType": "Transfer", "PaymentStatus": "WP", "Request": "test", "Special": 1, "Floor": "2", "Table": "B12,B14"}, {"Order No": "SWD2025070007", "BookID": 109, "ZoneID": 3, "Use Date": "2025-07-06", "Amount": "1000", "Create Date": "2025-07-06 16:51:35", "Customer": "<PERSON><PERSON><PERSON>", "Phone": "029009000", "Guests": {"Adults": 0, "Children": 0, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "ABCD", "Agent": "7Day", "PaymentType": null, "PaymentStatus": "WP", "Request": "Entire Floor Booking (Entire Floor Booking)", "Special": 1, "Floor": "3", "Table": "C11,C12,C13,C14,C15,C16,C17,C18,C19,C110,C111,C112,C113,C114,C115,C21,C22,C23,C24,C25,C26,C27,C28,C29,C210,C211,C212,C213,C214,C215,C31,C32,C33,C34,C35,C36,C37,C38,C39,C310,C311,C312,C313,C314,C315,C41,C42,C43,C44,C45,C46,C47,C48,C49,C410,C411,C412,C413,C414,C415,C51,C52,C53,C54,C55,C56,C57,C58,C59,C510,C511,C512,C513,C514,C515,C61,C62,C63,C64,C65,C66,C67,C68,C69,C610,C611,C612,C613,C614,C615,C71,C72,C73,C74,C75,C76,C77,C78,C79,C710,C711,C712,C713,C714,C715,C81,C82,C83,C84,C85,C86,C87,C88,C89,C810,C811,C812,C813,C814,C815"}]