<?php
// Include database connection
include '../dbconnect/_dbconnect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

// Connect to database
$conn = db_connect();

// Handle different request methods
switch ($method) {
    case 'GET':
        // Get all agents or a specific agent
        if (isset($_GET['id'])) {
            // Get a specific agent
            $id = $_GET['id'];
            $sql = "SELECT * FROM kp_agent WHERE id = :id";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $agent = $stmt->fetch(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $agent]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Agent not found']);
            }
        } else {
            // Get all agents
            $search = isset($_GET['search']) ? $_GET['search'] : '';
            
            if (!empty($search)) {
                // Search for agents
                $sql = "SELECT * FROM kp_agent WHERE name LIKE :search OR contact_person LIKE :search OR phone LIKE :search OR email LIKE :search ORDER BY name ASC";
                $stmt = $conn->prepare($sql);
                $searchParam = "%{$search}%";
                $stmt->bindParam(':search', $searchParam, PDO::PARAM_STR);
            } else {
                // Get all agents
                $sql = "SELECT * FROM kp_agent ORDER BY name ASC";
                $stmt = $conn->prepare($sql);
            }
            
            $stmt->execute();
            $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'data' => $agents]);
        }
        break;
        
    case 'POST':
        // Add a new agent
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            // If JSON parsing fails, try to get data from POST
            $data = $_POST;
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'Agent name is required']);
            exit;
        }
        
        // Prepare the SQL statement
        $sql = "INSERT INTO kp_agent (name, contact_person, phone, email, address, status) 
                VALUES (:name, :contact_person, :phone, :email, :address, :status)";
        
        $stmt = $conn->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
        $stmt->bindParam(':contact_person', $data['contact_person'], PDO::PARAM_STR);
        $stmt->bindParam(':phone', $data['phone'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':address', $data['address'], PDO::PARAM_STR);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_INT);
        
        // Execute the statement
        if ($stmt->execute()) {
            $newId = $conn->lastInsertId();
            echo json_encode(['success' => true, 'message' => 'Agent added successfully', 'id' => $newId]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add agent']);
        }
        break;
        
    case 'PUT':
        // Update an existing agent
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        if (empty($data['id']) || empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'Agent ID and name are required']);
            exit;
        }
        
        // Prepare the SQL statement
        $sql = "UPDATE kp_agent 
                SET name = :name, 
                    contact_person = :contact_person, 
                    phone = :phone, 
                    email = :email, 
                    address = :address, 
                    status = :status,
                    update_date = NOW()
                WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':id', $data['id'], PDO::PARAM_INT);
        $stmt->bindParam(':name', $data['name'], PDO::PARAM_STR);
        $stmt->bindParam(':contact_person', $data['contact_person'], PDO::PARAM_STR);
        $stmt->bindParam(':phone', $data['phone'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $data['email'], PDO::PARAM_STR);
        $stmt->bindParam(':address', $data['address'], PDO::PARAM_STR);
        $stmt->bindParam(':status', $data['status'], PDO::PARAM_INT);
        
        // Execute the statement
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Agent updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update agent']);
        }
        break;
        
    case 'DELETE':
        // Delete an agent
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data && isset($_GET['id'])) {
            $data = ['id' => $_GET['id']];
        }
        
        // Validate required fields
        if (empty($data['id'])) {
            echo json_encode(['success' => false, 'message' => 'Agent ID is required']);
            exit;
        }
        
        // Prepare the SQL statement
        $sql = "DELETE FROM kp_agent WHERE id = :id";
        $stmt = $conn->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':id', $data['id'], PDO::PARAM_INT);
        
        // Execute the statement
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Agent deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete agent']);
        }
        break;
        
    default:
        // Method not allowed
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}

// Close database connection
db_close($conn);
?>
