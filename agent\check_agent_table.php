<?php
// Include database connection
include '../dbconnect/_dbconnect.php';

// Connect to database
$conn = db_connect();

// Check if kp_agent table exists
$sql = "SHOW TABLES LIKE 'kp_agent'";
$result = $conn->query($sql);
$tableExists = $result->rowCount() > 0;

echo "<h2>Agent Table Check</h2>";
if ($tableExists) {
    echo "<p>The kp_agent table exists.</p>";
    
    // Get table structure
    $sql = "DESCRIBE kp_agent";
    $result = $conn->query($sql);
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Get sample data
    $sql = "SELECT * FROM kp_agent LIMIT 5";
    $result = $conn->query($sql);
    
    echo "<h3>Sample Data:</h3>";
    if ($result->rowCount() > 0) {
        echo "<table border='1'>";
        
        // Get column names
        $row = $result->fetch(PDO::FETCH_ASSOC);
        echo "<tr>";
        foreach ($row as $key => $value) {
            echo "<th>" . $key . "</th>";
        }
        echo "</tr>";
        
        // Output first row
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
        
        // Output remaining rows
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No data in the table.</p>";
    }
} else {
    echo "<p>The kp_agent table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE kp_agent (
        id INT(11) NOT NULL AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        status TINYINT(1) DEFAULT 1,
        create_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        update_date DATETIME ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )";
    
    try {
        $conn->exec($sql);
        echo "<p>Table created successfully!</p>";
    } catch (PDOException $e) {
        echo "<p>Error creating table: " . $e->getMessage() . "</p>";
    }
}

// Close connection
db_close($conn);
?>
