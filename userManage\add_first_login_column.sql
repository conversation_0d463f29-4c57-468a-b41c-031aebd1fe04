-- Add first_login column to kp_login table to track first-time login requirement
-- This script is safe to run multiple times

-- Check if first_login column exists
SET @exists_first_login = 0;
SELECT COUNT(*) INTO @exists_first_login
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_login'
    AND column_name = 'first_login';

-- Add the first_login column if it doesn't exist
SET @sql_first_login = IF(
    @exists_first_login = 0,
    'ALTER TABLE kp_login ADD COLUMN first_login TINYINT(1) NOT NULL DEFAULT 0 COMMENT "1 = User must change password on first login, 0 = Normal login" AFTER status',
    'SELECT "Column first_login already exists" as message'
);

PREPARE stmt FROM @sql_first_login;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing users to not require password change (they already have their passwords set)
UPDATE kp_login SET first_login = 0 WHERE first_login IS NULL OR first_login = 1;

-- Display result
SELECT 
    CASE 
        WHEN @exists_first_login = 0 THEN 'first_login column added successfully'
        ELSE 'first_login column already exists'
    END as result;
