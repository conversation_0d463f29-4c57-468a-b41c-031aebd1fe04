<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $pdo = db_connect();

    // --- รับค่าจาก POST แบบปลอดภัยและแน่นอน ---
    $booking_id        = $_POST['booking_id'] ?? '';
    $bookingid         = intval($_POST['dataid'] ?? 0);
    $customer_name     = $_POST['customer_name'] ?? '';
    $phone             = $_POST['phone'] ?? '';
    $adults            = intval($_POST['adults'] ?? 0);
    $children          = intval($_POST['children'] ?? 0);
    $infants           = intval($_POST['infants'] ?? 0);
    $guide             = intval($_POST['guide'] ?? 0);
    $foc               = intval($_POST['foc'] ?? 0);
    $leader            = intval($_POST['leader'] ?? 0);
    $voucher           = $_POST['voucher'] ?? '';
    $agent             = $_POST['agent'] ?? '';
    $amount            = floatval($_POST['amount'] ?? 0);
    $remark            = $_POST['remark'] ?? '';
    $payment_type      = $_POST['payment_type'] ?? 'Transfer';
    $special_request   = intval($_POST['special_request'] ?? 0);

    // --- ตรวจสอบค่าที่จำเป็น ---
    if (empty($bookingid)) {
        echo json_encode(['success' => false, 'message' => 'Booking ID is required']);
        exit;
    }

    if (empty($customer_name)) {
        echo json_encode(['success' => false, 'message' => 'Customer name is required']);
        exit;
    }

    // --- ตรวจสอบว่ารายการ booking มีจริงหรือไม่ ---
    $checkStmt = $pdo->prepare("SELECT booking_id FROM kp_booking WHERE booking_id = ?");
    $checkStmt->execute([$bookingid]);

    if (!$checkStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // --- ตรวจสอบว่ามีคอลัมน์ payment_type หรือไม่ ---
    $columnCheckStmt = $pdo->prepare("SHOW COLUMNS FROM kp_booking LIKE 'payment_type'");
    $columnCheckStmt->execute();
    $paymentTypeExists = $columnCheckStmt->fetch() !== false;

    // --- SQL สำหรับอัปเดตข้อมูล ---
    if ($paymentTypeExists) {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        remark = ?,
                        pay_type = ?,
                        special_request = ?,
                        update_date = NOW()
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $leader,
            $voucher,
            $agent,
            $amount,
            $remark,
            $payment_type,
            $special_request,
            $bookingid
        ];
    } else {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        pay_type = ?,
                        remark = ?,
                        special_request = ?,
                        update_date = NOW()
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $leader,
            $voucher,
            $agent,
            $amount,
            $payment_type,
            $remark,
            $special_request,
            $bookingid
        ];
    }

    // ลอง execute
    $updateStmt = $pdo->prepare($updateSql);
    $result = $updateStmt->execute($updateParams);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Booking updated successfully',
            'booking_id' => $bookingid
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update booking']);
    }

} catch (PDOException $e) {
    // Log ช่วย debug
    error_log("Database error in update_booking.php: " . $e->getMessage());
    error_log("POST DATA: " . print_r($_POST, true));
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred',
        'error' => $e->getMessage() // <-- เฉพาะตอนพัฒนา อย่าลืมลบออกก่อนขึ้น production
    ]);
} catch (Exception $e) {
    error_log("General error in update_booking.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the booking',
        'error' => $e->getMessage()
    ]);
}
?>
