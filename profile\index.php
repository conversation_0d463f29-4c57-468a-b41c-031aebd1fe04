<?php
/**
 * User Profile Page
 * 
 * This page allows users to view and update their profile
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/User.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Generate CSRF token
$csrf_token = Session::generateCsrfToken();

// Create User model instance
$userModel = new User();

// Get current user data
$userId = Session::get('id');
$userData = $userModel->getUserById($userId);

// Include header
$pageTitle = "My Profile";
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <div class="text-center mt-4">
                        <img src="../assets/images/profile/user-default.jpg" class="rounded-circle" width="150" alt="User Profile">
                        <h4 class="mt-3"><?php echo $userData['name']; ?></h4>
                        <h6 class="card-subtitle"><?php echo ucfirst($userData['role']); ?></h6>
                        <div class="mt-3">
                            <span class="badge bg-<?php echo $userData['status'] ? 'success' : 'secondary'; ?>">
                                <?php echo $userData['status'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body border-top">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h4 class="fw-semibold mb-1">
                                <?php 
                                    // Get the date the user was created
                                    $createDate = new DateTime($userData['create_date']);
                                    $now = new DateTime();
                                    $interval = $createDate->diff($now);
                                    echo $interval->days;
                                ?>
                            </h4>
                            <p class="text-muted mb-0">Days Active</p>
                        </div>
                        <div class="col-6">
                            <h4 class="fw-semibold mb-1">
                                <?php 
                                    // Format the date the user was last updated
                                    echo date('Y-m-d', strtotime($userData['update_date']));
                                ?>
                            </h4>
                            <p class="text-muted mb-0">Last Updated</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Update Profile</h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_GET['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            Profile updated successfully.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_GET['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php 
                            $message = '';
                            switch ($_GET['error']) {
                                case 'invalid_token':
                                    $message = 'Invalid security token. Please try again.';
                                    break;
                                case 'invalid_email':
                                    $message = 'Please enter a valid email address.';
                                    break;
                                case 'password_mismatch':
                                    $message = 'New passwords do not match.';
                                    break;
                                case 'current_password':
                                    $message = 'Current password is incorrect.';
                                    break;
                                default:
                                    $message = 'An error occurred while updating your profile.';
                            }
                            echo $message;
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form id="profileForm" action="update_profile.php" method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" value="<?php echo $userData['user']; ?>" readonly>
                            <div class="form-text">Username cannot be changed.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo $userData['name']; ?>" required>
                            <div class="invalid-feedback">Please enter your full name.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo $userData['email']; ?>" required>
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h5>Change Password</h5>
                        <p class="text-muted">Leave blank if you don't want to change your password.</p>
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                            <div class="invalid-feedback">Please enter your current password.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password">
                            
                            <!-- Password strength indicator -->
                            <div class="password-strength mt-2">
                                <div class="password-strength-bar" id="passwordStrengthBar"></div>
                            </div>
                            <div class="password-feedback" id="passwordFeedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            <div class="invalid-feedback">Passwords do not match.</div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .password-strength {
        height: 5px;
        width: 100%;
        background: #ddd;
        border-radius: 3px;
    }
    .password-strength-bar {
        height: 100%;
        border-radius: 3px;
        transition: width 0.3s ease;
        width: 0%;
    }
    .weak { width: 25%; background-color: #ff4d4d; }
    .medium { width: 50%; background-color: #ffaa00; }
    .strong { width: 75%; background-color: #73e600; }
    .very-strong { width: 100%; background-color: #00b33c; }
    .password-feedback {
        font-size: 12px;
        margin-top: 5px;
        color: #666;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password strength checker
    $("#new_password").on("input", function() {
        var password = $(this).val();
        var strength = 0;
        var feedback = "";

        if (password.length > 0) {
            // Check password length
            if (password.length >= 8) {
                strength += 1;
            } else {
                feedback += "Password should be at least 8 characters. ";
            }

            // Check for uppercase letters
            if (/[A-Z]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add uppercase letters. ";
            }

            // Check for lowercase letters
            if (/[a-z]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add lowercase letters. ";
            }

            // Check for numbers
            if (/[0-9]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add numbers. ";
            }

            // Check for special characters
            if (/[^A-Za-z0-9]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add special characters. ";
            }

            // Update strength bar
            var $strengthBar = $("#passwordStrengthBar");
            $strengthBar.removeClass("weak medium strong very-strong");

            if (strength === 0) {
                $strengthBar.css("width", "0%");
            } else if (strength <= 2) {
                $strengthBar.addClass("weak");
            } else if (strength <= 3) {
                $strengthBar.addClass("medium");
            } else if (strength <= 4) {
                $strengthBar.addClass("strong");
            } else {
                $strengthBar.addClass("very-strong");
            }

            // Update feedback
            $("#passwordFeedback").text(feedback);
        } else {
            $("#passwordStrengthBar").css("width", "0%").removeClass("weak medium strong very-strong");
            $("#passwordFeedback").text("");
        }
    });

    // Password confirmation validation
    $("#confirm_password").on("input", function() {
        var password = $("#new_password").val();
        var confirmPassword = $(this).val();
        
        if (password !== confirmPassword) {
            $(this).addClass("is-invalid");
        } else {
            $(this).removeClass("is-invalid");
        }
    });

    // Form validation
    $("#profileForm").on("submit", function(event) {
        var isValid = true;
        
        // Check if password fields are filled
        var currentPassword = $("#current_password").val();
        var newPassword = $("#new_password").val();
        var confirmPassword = $("#confirm_password").val();
        
        // If any password field is filled, all must be filled
        if (currentPassword || newPassword || confirmPassword) {
            if (!currentPassword) {
                $("#current_password").addClass("is-invalid");
                isValid = false;
            } else {
                $("#current_password").removeClass("is-invalid");
            }
            
            if (newPassword !== confirmPassword) {
                $("#confirm_password").addClass("is-invalid");
                isValid = false;
            } else {
                $("#confirm_password").removeClass("is-invalid");
            }
        }
        
        if (!isValid) {
            event.preventDefault();
        }
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
