<?php
/**
 * Create JSON file on floorplan page load
 * 
 * This file is included in the floorplan index.php to generate a JSON file
 * containing booking data for the current date when the page is loaded.
 */

// Include database connection
include_once('../dbconnect/_dbconnect.php');

// Get current date in YYYY-MM-DD format
$today = date('Y-m-d');

// Log the action
$log_message = "Creating JSON file for date: " . $today . " at " . date('Y-m-d H:i:s');
error_log($log_message);

// Create JSON file for today's date
create_json_file_by_date($today);

// Also create JSON file for zone data
create_json_file_by_zone($today);

// Return success message if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'message' => 'JSON files created successfully',
        'date' => $today,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
