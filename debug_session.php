<?php
session_start();

echo "<h2>Session Debug Information</h2>";

echo "<h3>Current Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Login Status:</h3>";
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true) {
    echo "<p style='color: green;'>✅ User is logged in</p>";
    echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
    echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'Not set') . "</p>";
    echo "<p><strong>Name:</strong> " . ($_SESSION['name'] ?? 'Not set') . "</p>";
    echo "<p><strong>User ID:</strong> " . ($_SESSION['id'] ?? 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>❌ User is not logged in</p>";
}

echo "<h3>Role Check Test:</h3>";
try {
    require_once 'includes/role_check.php';
    
    echo "<p><strong>isLoggedIn():</strong> " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>getCurrentUserRole():</strong> " . (getCurrentUserRole() ?? 'None') . "</p>";
    echo "<p><strong>isSuperAdmin():</strong> " . (isSuperAdmin() ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>isAdmin():</strong> " . (isAdmin() ? 'Yes' : 'No') . "</p>";
    
    echo "<h4>Testing requireRole('admin'):</h4>";
    try {
        requireRole('admin');
        echo "<p style='color: green;'>✅ Admin access granted</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Admin access denied: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading role_check.php: " . $e->getMessage() . "</p>";
}

echo "<h3>Database Connection Test:</h3>";
try {
    require_once 'dbconnect/_dbconnect.php';
    $conn = db_connect();
    if ($conn) {
        echo "<p style='color: green;'>✅ Database connection successful</p>";
        
        // Check if user exists in database
        if (isset($_SESSION['username'])) {
            $stmt = $conn->prepare("SELECT * FROM kp_login WHERE user = ?");
            $stmt->execute([$_SESSION['username']]);
            $user = $stmt->fetch();
            
            if ($user) {
                echo "<p style='color: green;'>✅ User found in database</p>";
                echo "<p><strong>DB Username:</strong> " . $user['user'] . "</p>";
                echo "<p><strong>DB Role:</strong> " . ($user['role'] ?? 'Not set') . "</p>";
                echo "<p><strong>DB Status:</strong> " . ($user['status'] ?? 'Not set') . "</p>";
                echo "<p><strong>DB Name:</strong> " . ($user['name'] ?? 'Not set') . "</p>";
            } else {
                echo "<p style='color: red;'>❌ User not found in database</p>";
            }
        }
        
        db_close($conn);
    } else {
        echo "<p style='color: red;'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h3>Actions:</h3>";
echo "<p><a href='login/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; margin-right: 10px;'>Go to Login</a>";
echo "<a href='fix_login_issue.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; margin-right: 10px;'>Fix Login Issues</a>";
echo "<a href='userManage/' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none;'>Try User Management</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    overflow-x: auto;
}

p {
    margin: 5px 0;
}
</style>
