<?php
session_start();
require_once '../dbconnect/_dbconnect.php';

echo "<h2>Reset Password Functionality Test</h2>";

// Check current user session
echo "<h3>1. Current User Session</h3>";
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true) {
    echo "<p><strong>User:</strong> " . htmlspecialchars($_SESSION['username']) . "</p>";
    echo "<p><strong>Role:</strong> " . htmlspecialchars($_SESSION['role']) . "</p>";
    echo "<p><strong>User ID:</strong> " . htmlspecialchars($_SESSION['id']) . "</p>";
    
    $canReset = ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super_admin');
    echo "<p><strong>Can Reset Passwords:</strong> " . ($canReset ? '✅ Yes' : '❌ No') . "</p>";
} else {
    echo "<p style='color: red;'>❌ Not logged in</p>";
    echo "<p><a href='../login/'>Login here</a></p>";
    exit;
}

try {
    $pdo = db_connect();
    
    // Check default password settings
    echo "<h3>2. Default Password Configuration</h3>";
    $defaultPassword = 'pwd123';
    echo "<p><strong>Default Password:</strong> <code>$defaultPassword</code></p>";
    
    // Test password hashing
    $hashedPassword = password_hash($defaultPassword, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3,
    ]);
    echo "<p><strong>Hashed Password:</strong> <code>" . substr($hashedPassword, 0, 50) . "...</code></p>";
    
    $verifyResult = password_verify($defaultPassword, $hashedPassword);
    echo "<p><strong>Hash Verification:</strong> " . ($verifyResult ? '✅ Pass' : '❌ Fail') . "</p>";
    
    // List users that can be reset
    echo "<h3>3. Users Available for Password Reset</h3>";
    $stmt = $pdo->prepare("SELECT id, user, role, status, first_login FROM kp_login WHERE id != ? ORDER BY user");
    $stmt->execute([$_SESSION['id']]);
    $users = $stmt->fetchAll();
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Status</th><th>First Login</th><th>Action</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['user']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . ($user['status'] ? 'Active' : 'Inactive') . "</td>";
            echo "<td>" . ($user['first_login'] ? 'Yes' : 'No') . "</td>";
            
            if ($canReset) {
                echo "<td><button onclick=\"testResetPassword(" . $user['id'] . ", '" . htmlspecialchars($user['user']) . "')\">Reset Password</button></td>";
            } else {
                echo "<td><em>No permission</em></td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No other users found.</p>";
    }
    
    // Check API file
    echo "<h3>4. API File Check</h3>";
    $apiFile = 'api/reset_password.php';
    if (file_exists($apiFile)) {
        echo "<p>✅ <strong>Reset Password API:</strong> File exists</p>";
    } else {
        echo "<p>❌ <strong>Reset Password API:</strong> File missing</p>";
    }
    
    echo "<h3>5. Testing Instructions</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Manual Testing Steps:</h4>";
    echo "<ol>";
    echo "<li>Go to <a href='user_management.php' target='_blank'>User Management Page</a></li>";
    echo "<li>Check if Reset Password button (key icon) is visible in Actions column</li>";
    echo "<li>Click the Reset Password button for a test user</li>";
    echo "<li>Confirm the reset action</li>";
    echo "<li>Verify the success message shows default password 'pwd123'</li>";
    echo "<li>Try logging in as that user with the default password</li>";
    echo "<li>Should be redirected to change password page</li>";
    echo "</ol>";
    
    echo "<h4>Role-Based Visibility:</h4>";
    echo "<ul>";
    echo "<li><strong>Admin/Super Admin:</strong> Should see Reset Password button</li>";
    echo "<li><strong>Regular User:</strong> Should NOT see Reset Password button</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<script>
function testResetPassword(userId, username) {
    if (confirm(`Reset password for user "${username}"?`)) {
        fetch('api/reset_password.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Success: ${data.message}\nDefault Password: ${data.default_password}`);
                location.reload();
            } else {
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            alert('Error: Failed to reset password');
            console.error('Error:', error);
        });
    }
}
</script>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { width: 100%; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
    button { padding: 5px 10px; background-color: #f39c12; color: white; border: none; border-radius: 3px; cursor: pointer; }
    button:hover { background-color: #e67e22; }
</style>
