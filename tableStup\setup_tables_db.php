<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();

    // Read SQL file
    $sql = file_get_contents('create_tables_table.sql');

    // Execute SQL
    $conn->exec($sql);

    echo "Tables database setup completed successfully!";
    echo "<br><br><a href='table_setup.php' class='btn btn-primary'>Go to Table Setup</a>";

} catch (PDOException $e) {
    echo "Database Error: " . $e->getMessage();
}
?>
