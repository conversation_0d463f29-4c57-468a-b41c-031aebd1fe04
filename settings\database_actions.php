<?php
/**
 * Database Actions Handler
 * 
 * Handles database maintenance operations
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/Database.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!Session::isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if user has admin role
if (Session::get('role') !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'You do not have permission to perform this action']);
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Validate CSRF token
$token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
if (!Session::validateCsrfToken($token)) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

// Get action
$action = isset($_POST['action']) ? Security::sanitizeInput($_POST['action']) : '';

// Get database instance
$db = Database::getInstance();

// Process action
switch ($action) {
    case 'backup':
        // Backup database
        handleBackupDatabase($db);
        break;
    
    case 'optimize':
        // Optimize database
        handleOptimizeDatabase($db);
        break;
    
    case 'clear_cache':
        // Clear cache
        handleClearCache();
        break;
    
    default:
        // Invalid action
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        exit;
}

/**
 * Handle database backup
 * 
 * @param Database $db Database instance
 */
function handleBackupDatabase($db) {
    try {
        // Create backup directory if it doesn't exist
        $backupDir = dirname(__DIR__) . '/backups';
        if (!file_exists($backupDir)) {
            mkdir($backupDir, 0777, true);
        }
        
        // Generate backup filename
        $timestamp = date('Y-m-d_H-i-s');
        $backupFile = $backupDir . '/backup_' . $timestamp . '.sql';
        
        // Get database configuration
        $host = DB_HOST;
        $user = DB_USER;
        $pass = DB_PASS;
        $name = DB_NAME;
        
        // Create backup command
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s %s > %s',
            escapeshellarg($host),
            escapeshellarg($user),
            escapeshellarg($pass),
            escapeshellarg($name),
            escapeshellarg($backupFile)
        );
        
        // Execute backup command
        exec($command, $output, $returnVar);
        
        if ($returnVar === 0) {
            // Log the action
            error_log("User {$_SESSION['username']} created database backup: {$backupFile}");
            
            // Return success response
            echo json_encode([
                'success' => true, 
                'message' => 'Database backup created successfully: ' . basename($backupFile)
            ]);
        } else {
            // Return error response
            echo json_encode([
                'success' => false, 
                'message' => 'Failed to create database backup'
            ]);
        }
    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'success' => false, 
            'message' => 'An error occurred: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle database optimization
 * 
 * @param Database $db Database instance
 */
function handleOptimizeDatabase($db) {
    try {
        // Get all tables
        $tables = $db->getRows("SHOW TABLES");
        $tableNames = [];
        
        foreach ($tables as $table) {
            $tableName = reset($table); // Get the first (and only) value from the row
            $tableNames[] = $tableName;
        }
        
        if (empty($tableNames)) {
            echo json_encode([
                'success' => false, 
                'message' => 'No tables found to optimize'
            ]);
            return;
        }
        
        // Optimize tables
        $query = "OPTIMIZE TABLE " . implode(', ', $tableNames);
        $result = $db->query($query);
        
        if ($result) {
            // Log the action
            error_log("User {$_SESSION['username']} optimized database tables");
            
            // Return success response
            echo json_encode([
                'success' => true, 
                'message' => 'Database tables optimized successfully'
            ]);
        } else {
            // Return error response
            echo json_encode([
                'success' => false, 
                'message' => 'Failed to optimize database tables'
            ]);
        }
    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'success' => false, 
            'message' => 'An error occurred: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle cache clearing
 */
function handleClearCache() {
    try {
        // Define cache directories
        $cacheDirs = [
            dirname(__DIR__) . '/cache',
            dirname(__DIR__) . '/json'
        ];
        
        $filesDeleted = 0;
        
        // Clear each cache directory
        foreach ($cacheDirs as $cacheDir) {
            if (file_exists($cacheDir)) {
                $files = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($cacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::CHILD_FIRST
                );
                
                foreach ($files as $file) {
                    if ($file->isFile()) {
                        // Skip .gitkeep files
                        if ($file->getFilename() === '.gitkeep') {
                            continue;
                        }
                        
                        // Delete file
                        if (unlink($file->getRealPath())) {
                            $filesDeleted++;
                        }
                    }
                }
            }
        }
        
        // Log the action
        error_log("User {$_SESSION['username']} cleared system cache ({$filesDeleted} files deleted)");
        
        // Return success response
        echo json_encode([
            'success' => true, 
            'message' => "{$filesDeleted} cache files cleared successfully"
        ]);
    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'success' => false, 
            'message' => 'An error occurred: ' . $e->getMessage()
        ]);
    }
}
