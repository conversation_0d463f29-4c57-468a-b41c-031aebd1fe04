<!-- Import Js Files -->
<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
<script src="../assets/js/theme/app.init.js"></script>
<script src="../assets/js/theme/theme.js"></script>
<script src="../assets/js/theme/app.min.js"></script>
<script src="../assets/js/theme/sidebarmenu.js"></script>


<!-- solar icons -->
<script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
<!-- <script src="../assets/libs/apexcharts/dist/apexcharts.min.js"></script> -->
<!-- <script src="../assets/js/dashboards/dashboard1.js"></script> -->
<!-- <script src="../assets/libs/fullcalendar/index.global.min.js"></script> -->

<!-- jQuery -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<?php
//2025-03-31
$today = date('Y-m-d');
?>

<!-- get data -->
<script>
  // auto load data every 5 seconds from server using ajax and update table
  $(document).ready(function () {
    setInterval(function () {
      $.ajax({
        // url: "../api/getBookingbyZone.php",
        url: "../api/json_by_month.json?timestamp=" + new Date().getTime(),
        // url: "../api/getBookingbyZone.php?timestamp=" + new Date().getTime(),
        type: "GET",
        dataType: "json",
        success: function (data) {
          // console.log(data);
          var table = "";
          var i = 1;
          $.each(data, function (key, value) {

            let current_date = new Date(value.Date);
            let day = current_date.getDate().toString().padStart(2, '0');
            let month = (current_date.getMonth() + 1).toString().padStart(2, '0');
            let year = current_date.getFullYear();
            let weekday = current_date.toLocaleString('en-us', { weekday: 'long' });

            console.log(value.Date);

            if (value.Date == "<?php echo $today; ?>") {
              table += `<tr class="table-info">`;
              table += `<td class="bg-info"><strong>${day}</strong><small class="text-muted">/${month}/${year}</small><br/>${weekday}</td>`;

              // Rooftop Edge
              table += `<td class="text-center">`;
              if(value.RooftopE.t_Value > value.RooftopE.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.RooftopE.t_Value}</strong>/${value.RooftopE.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.RooftopE.p_Value}</strong>/${value.RooftopE.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Rooftop Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.RooftopM.p_Value > value.RooftopM.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.RooftopM.p_Value}</strong>/${value.RooftopM.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Prow Edge
              table += `<td class="text-center">`;
              if(value.ProwE.t_Value > value.ProwE.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.ProwE.t_Value}</strong>/${value.ProwE.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.ProwE.p_Value}</strong>/${value.ProwE.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Prow Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.ProwM.p_Value > value.ProwM.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.ProwM.p_Value}</strong>/${value.ProwM.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Fl.2 Edge
              table += `<td class="text-center">`;
              if(value.Fl2E.t_Value > value.Fl2E.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.Fl2E.t_Value}</strong>/${value.Fl2E.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.Fl2E.p_Value}</strong>/${value.Fl2E.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Fl.2 Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.Fl2M.p_Value > value.Fl2M.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.Fl2M.p_Value}</strong>/${value.Fl2M.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Fl.1 Edge
              table += `<td class="text-center">`;
              if(value.Fl1E.t_Value > value.Fl1E.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.Fl1E.t_Value}</strong>/${value.Fl1E.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.Fl1M.p_Value}</strong>/${value.Fl1M.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Fl.1 Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.Fl1M.p_Value > value.Fl1M.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.Fl1M.p_Value}</strong>/${value.Fl1M.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // VIP1
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP1.t_Value >= value.VIP1.t_maxValue){
                console.log(value.VIP1.t_Value+" "+value.VIP1.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP1.t_Value+" "+value.VIP1.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP2
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP2.t_Value >= value.VIP2.t_maxValue){
                console.log(value.VIP2.t_Value+" "+value.VIP2.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP2.t_Value+" "+value.VIP2.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP3
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP3.t_Value >= value.VIP3.t_maxValue){
                console.log(value.VIP3.t_Value+" "+value.VIP3.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP3.t_Value+" "+value.VIP3.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP4
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP4.t_Value >= value.VIP4.t_maxValue){
                console.log(value.VIP4.t_Value+" "+value.VIP4.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP4.t_Value+" "+value.VIP4.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;
              
              table += `</tr>`;

            }else {
              table += `<tr>`;
              table += `<td class="bg-danger bg-opacity-10"><strong>${day}</strong><small class="text-muted">/${month}/${year}</small><br/>${weekday}</td>`;

              // Rooftop Edge
              table += `<td class="text-center bg-warning bg-opacity-10">`;
              if(value.RooftopE.t_Value > value.RooftopE.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.RooftopE.t_Value}</strong>/${value.RooftopE.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.RooftopE.p_Value}</strong>/${value.RooftopE.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Rooftop Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.RooftopM.p_Value > value.RooftopM.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.RooftopM.p_Value}</strong>/${value.RooftopM.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Prow Edge
              table += `<td class="text-center bg-info bg-opacity-10">`;
              if(value.ProwE.t_Value > value.ProwE.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.ProwE.t_Value}</strong>/${value.ProwE.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.ProwE.p_Value}</strong>/${value.ProwE.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Prow Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.ProwM.p_Value > value.ProwM.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.ProwM.p_Value}</strong>/${value.ProwM.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Fl.2 Edge
              table += `<td class="text-center bg-warning bg-opacity-10">`;
              if(value.Fl2E.t_Value > value.Fl2E.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.Fl2E.t_Value}</strong>/${value.Fl2E.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.Fl2E.p_Value}</strong>/${value.Fl2E.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Fl.2 Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.Fl2M.p_Value > value.Fl2M.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.Fl2M.p_Value}</strong>/${value.Fl2M.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // Fl.1 Edge
              table += `<td class="text-center bg-info bg-opacity-10">`;
              if(value.Fl1E.t_Value > value.Fl1E.t_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<strong>${value.Fl1E.t_Value}</strong>/${value.Fl1E.t_maxValue}<br/><span class="badge bg-danger-subtle text-danger"><strong>${value.Fl1M.p_Value}</strong>/${value.Fl1M.p_maxValue}</span>`;
              }
              table +=`</td>`;

              // Fl.1 Middle
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.Fl1M.p_Value > value.Fl1M.p_maxValue){
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                table += `<span class="badge bg-danger-subtle text-danger"><strong>${value.Fl1M.p_Value}</strong>/${value.Fl1M.p_maxValue}</span>`;
              }
              table +=`</div></td>`;

              // VIP1
              table += `<td class="text-center bg-success bg-opacity-10"><div class="mt-2">`;
              if(value.VIP1.t_Value >= value.VIP1.t_maxValue){
                console.log(value.VIP1.t_Value+" "+value.VIP1.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP1.t_Value+" "+value.VIP1.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP2
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP2.t_Value >= value.VIP2.t_maxValue){
                console.log(value.VIP2.t_Value+" "+value.VIP2.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP2.t_Value+" "+value.VIP2.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP3
              table += `<td class="text-center bg-success bg-opacity-10"><div class="mt-2">`;
              if(value.VIP3.t_Value >= value.VIP3.t_maxValue){
                console.log(value.VIP3.t_Value+" "+value.VIP3.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP3.t_Value+" "+value.VIP3.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;

              // VIP4
              table += `<td class="text-center"><div class="mt-2">`;
              if(value.VIP4.t_Value >= value.VIP4.t_maxValue){
                console.log(value.VIP4.t_Value+" "+value.VIP4.t_maxValue);
                table += `<i class="ti ti-square-rounded-x h4" style="color:red"></i>`;
              }else{
                console.log(value.VIP4.t_Value+" "+value.VIP4.t_maxValue);
                table += `<i class="ti ti-check h4" style="color:green"></i></i>`;
              }
              table +=`</div></td>`;
              
              table += `</tr>`;
            }
            
            
            i++;
          });
          $("#table").html(table);
        }
      });
    }, 5000); // every 5 seconds
  });
</script>