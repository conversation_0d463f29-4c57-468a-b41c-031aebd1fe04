<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Validate input
    if (!isset($_POST['id'])) {
        throw new Exception('Missing table ID');
    }
    
    $tableId = $_POST['id'];
    
    // Connect to database
    $conn = db_connect();
    
    // Check if table exists
    $checkSql = "SELECT COUNT(*) FROM kp_tables WHERE table_id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':id', $tableId);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() == 0) {
        throw new Exception('Table not found');
    }
    
    // Check if table is used in any bookings
    $bookingSql = "SELECT COUNT(*) FROM kp_booking WHERE FIND_IN_SET(:id, tables) > 0";
    $bookingStmt = $conn->prepare($bookingSql);
    $bookingStmt->bindParam(':id', $tableId);
    $bookingStmt->execute();
    
    if ($bookingStmt->fetchColumn() > 0) {
        throw new Exception('Cannot delete table that is used in bookings');
    }
    
    // Delete table
    $sql = "DELETE FROM kp_tables WHERE table_id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':id', $tableId);
    $stmt->execute();
    
    // Return success
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
