<?php
// Include database connection
include 'dbconnect/_dbconnect.php';

// Connect to database
$conn = db_connect();

echo "<h2>Login Issue Diagnostic and Fix</h2>";

try {
    // Check if kp_login table exists
    $sql = "SHOW TABLES LIKE 'kp_login'";
    $result = $conn->query($sql);
    
    if ($result->rowCount() > 0) {
        echo "<h3>✅ Login Table Exists</h3>";
        
        // Get table structure
        echo "<h4>Current Table Structure:</h4>";
        $sql = "DESCRIBE kp_login";
        $result = $conn->query($sql);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $hasStatusColumn = false;
        $hasRoleColumn = false;
        $hasNameColumn = false;
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
            
            if ($row['Field'] === 'status') $hasStatusColumn = true;
            if ($row['Field'] === 'role') $hasRoleColumn = true;
            if ($row['Field'] === 'name') $hasNameColumn = true;
        }
        echo "</table>";
        
        // Check current users
        echo "<h4>Current Users in Database:</h4>";
        $sql = "SELECT * FROM kp_login ORDER BY id";
        $result = $conn->query($sql);
        
        if ($result->rowCount() > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Name</th><th>Email</th><th>Role</th><th>Status</th><th>Password Info</th></tr>";
            
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . $row['user'] . "</td>";
                echo "<td>" . ($row['name'] ?? 'N/A') . "</td>";
                echo "<td>" . ($row['email'] ?? 'N/A') . "</td>";
                echo "<td>" . ($row['role'] ?? 'N/A') . "</td>";
                echo "<td>" . ($row['status'] ?? 'N/A') . "</td>";
                
                // Check password type
                $passInfo = password_get_info($row['pass']);
                if ($passInfo['algo'] === 0) {
                    echo "<td style='color: orange;'>Plain Text</td>";
                } else {
                    echo "<td style='color: green;'>Hashed (" . $passInfo['algoName'] . ")</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ No users found in database!</p>";
        }
        
        // Fix form
        echo "<h3>🔧 Fix Login Issues</h3>";
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 10px 0;'>";
        echo "<h4>What this will do:</h4>";
        echo "<ul>";
        echo "<li>Update table structure to include missing columns (status, role, name)</li>";
        echo "<li>Create/update admin user with username: <strong>admin</strong> and password: <strong>admin123</strong></li>";
        echo "<li>Set proper status and role values</li>";
        echo "<li>Hash passwords securely</li>";
        echo "</ul>";
        echo "</div>";
        echo "<input type='submit' name='fix_login' value='Fix Login Issues' style='padding: 15px 30px; background: #28a745; color: white; border: none; cursor: pointer; font-size: 16px;'>";
        echo "</form>";
        
        // Handle form submission
        if (isset($_POST['fix_login'])) {
            echo "<h4>🔧 Applying Fixes...</h4>";
            
            // Add missing columns if needed
            if (!$hasStatusColumn) {
                echo "<p>Adding status column...</p>";
                $conn->exec("ALTER TABLE kp_login ADD COLUMN status TINYINT(1) NOT NULL DEFAULT 1");
                echo "<p style='color: green;'>✅ Status column added</p>";
            }
            
            if (!$hasRoleColumn) {
                echo "<p>Adding role column...</p>";
                $conn->exec("ALTER TABLE kp_login ADD COLUMN role ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user'");
                echo "<p style='color: green;'>✅ Role column added</p>";
            }
            
            if (!$hasNameColumn) {
                echo "<p>Adding name column...</p>";
                $conn->exec("ALTER TABLE kp_login ADD COLUMN name VARCHAR(100) DEFAULT NULL");
                echo "<p style='color: green;'>✅ Name column added</p>";
            }
            
            // Create/update admin user
            echo "<p>Creating/updating admin user...</p>";
            
            // Hash the password using Argon2id
            $hashedPassword = password_hash('admin123', PASSWORD_ARGON2ID, [
                'memory_cost' => 65536, // 64 MB
                'time_cost' => 4,       // 4 iterations
                'threads' => 3          // 3 threads
            ]);
            
            // Check if admin user exists
            $checkSql = "SELECT id FROM kp_login WHERE user = 'admin'";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->execute();
            
            if ($checkStmt->rowCount() > 0) {
                // Update existing admin user
                $updateSql = "UPDATE kp_login SET pass = ?, name = ?, role = ?, status = 1 WHERE user = 'admin'";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->execute([$hashedPassword, 'Administrator', 'admin']);
                echo "<p style='color: green;'>✅ Admin user updated successfully!</p>";
            } else {
                // Create new admin user
                $insertSql = "INSERT INTO kp_login (user, pass, name, email, role, status, create_date) VALUES (?, ?, ?, ?, ?, 1, NOW())";
                $insertStmt = $conn->prepare($insertSql);
                $insertStmt->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>', 'admin']);
                echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
            }
            
            // Also create a super admin user
            echo "<p>Creating/updating super admin user...</p>";
            $superHashedPassword = password_hash('superadmin123', PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3
            ]);
            
            $checkSuperSql = "SELECT id FROM kp_login WHERE user = 'superadmin'";
            $checkSuperStmt = $conn->prepare($checkSuperSql);
            $checkSuperStmt->execute();
            
            if ($checkSuperStmt->rowCount() > 0) {
                $updateSuperSql = "UPDATE kp_login SET pass = ?, name = ?, role = ?, status = 1 WHERE user = 'superadmin'";
                $updateSuperStmt = $conn->prepare($updateSuperSql);
                $updateSuperStmt->execute([$superHashedPassword, 'Super Administrator', 'super_admin']);
                echo "<p style='color: green;'>✅ Super admin user updated successfully!</p>";
            } else {
                $insertSuperSql = "INSERT INTO kp_login (user, pass, name, email, role, status, create_date) VALUES (?, ?, ?, ?, ?, 1, NOW())";
                $insertSuperStmt = $conn->prepare($insertSuperSql);
                $insertSuperStmt->execute(['superadmin', $superHashedPassword, 'Super Administrator', '<EMAIL>', 'super_admin']);
                echo "<p style='color: green;'>✅ Super admin user created successfully!</p>";
            }
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
            echo "<h4>🎉 Login Issues Fixed Successfully!</h4>";
            echo "<p><strong>You can now login with:</strong></p>";
            echo "<ul>";
            echo "<li><strong>Admin:</strong> username = <code>admin</code>, password = <code>admin123</code></li>";
            echo "<li><strong>Super Admin:</strong> username = <code>superadmin</code>, password = <code>superadmin123</code></li>";
            echo "</ul>";
            echo "</div>";
            
            // Refresh the page to show updated data
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Login table (kp_login) does not exist!</p>";
        echo "<p>Please run the database setup scripts first.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Close connection
db_close($conn);

echo "<hr>";
echo "<p><a href='login/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; margin-right: 10px;'>Go to Login Page</a>";
echo "<a href='check_users.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none;'>Check Users</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    width: 100%;
    max-width: 1000px;
}

th {
    background: #007cba;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

tr:nth-child(even) {
    background: #f9f9f9;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
