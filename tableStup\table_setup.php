<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}
include_once("header.php");
?>

<body>
  <?php
    include_once("../_loader.php");
  ?>

  <div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <div class="iconbar">
            <div>
                <?php
                    include_once("_menuMain.php");
                    include_once("_menuPages.php");
                ?>
            </div>
        </div>
    </aside>
    <!--  Sidebar End -->

    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h4 class="card-title mb-1">Table Setup</h4>
                                        <p class="card-subtitle mb-0">Manage tables on Floor 1</p>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <div class="input-group date datepicker me-3" style="z-index: 10 !important;">
                                            <input type="text" class="form-control" id="myDate" name="myDate" value="<?php echo date('d-m-Y'); ?>" readonly style="width: 120px !important;">
                                            <span class="input-group-addon input-group-text">
                                                <i class="ti ti-calendar fs-5"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Loading Overlay -->
                                <div id="loading-overlay" class="loading-overlay" style="display: none;">
                                    <div class="spinner-container">
                                        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <div class="mt-2 text-primary fw-bold">Loading...</div>
                                        <div class="mt-2 text-secondary">Updating table data</div>
                                    </div>
                                </div>
                                <ul class="nav nav-tabs theme-tab gap-3 flex-nowrap" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#table-status" role="tab" aria-selected="true">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:widget-linear" class="fs-4"></iconify-icon>
                                                <span>Table Status</span>
                                            </div>
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link" data-bs-toggle="tab" href="#add-tables" role="tab" aria-selected="false">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:add-circle-linear" class="fs-4"></iconify-icon>
                                                <span>Add Tables</span>
                                            </div>
                                        </a>
                                    </li>
                                </ul>

                                <div class="tab-content">
                                    <!-- Table Status Tab -->
                                    <div class="tab-pane fade show active" id="table-status" role="tabpanel">
                                        <div class="p-3">
                                            <div class="row mb-4">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="table-row-filter" class="form-label">Filter by Row</label>
                                                        <select id="table-row-filter" class="form-select">
                                                            <option value="all">All Rows</option>
                                                            <option value="1A">Row 1A</option>
                                                            <option value="1B">Row 1B</option>
                                                            <option value="1C">Row 1C</option>
                                                            <option value="1D">Row 1D</option>
                                                            <option value="1E">Row 1E</option>
                                                            <option value="1F">Row 1F</option>
                                                            <option value="1G">Row 1G</option>
                                                            <option value="1H">Row 1H</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="table-status-filter" class="form-label">Filter by Status</label>
                                                        <select id="table-status-filter" class="form-select">
                                                            <option value="all">All Statuses</option>
                                                            <option value="active">Active</option>
                                                            <option value="disabled">Disabled</option>
                                                            <option value="disabled-on-date">Disabled on Selected Date</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label class="form-label">Selected Date Status</label>
                                                        <div class="d-flex align-items-center">
                                                            <span id="date-status-indicator" class="badge bg-info me-2">
                                                                <i class="ti ti-calendar me-1"></i>
                                                                <span id="selected-date-display"><?php echo date('d-m-Y'); ?></span>
                                                            </span>
                                                            <small class="text-muted">Viewing tables for this date</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="table-responsive">
                                                <table id="table-list" class="table table-hover align-middle">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th style="width: 20%;">
                                                                <i class="ti ti-table me-1"></i>Table ID
                                                            </th>
                                                            <th style="width: 15%;">
                                                                <i class="ti ti-layout-rows me-1"></i>Row
                                                            </th>
                                                            <th style="width: 25%;">
                                                                <i class="ti ti-status-change me-1"></i>Status
                                                            </th>
                                                            <th style="width: 20%;">
                                                                <i class="ti ti-calendar me-1"></i>Date Info
                                                            </th>
                                                            <th style="width: 20%;">
                                                                <i class="ti ti-settings me-1"></i>Actions
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Table data will be loaded here via AJAX -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add Tables Tab -->
                                    <div class="tab-pane fade" id="add-tables" role="tabpanel">
                                        <div class="p-3">
                                            <form id="add-table-form">
                                                <div class="row mb-3">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-row" class="form-label">Row</label>
                                                            <select id="table-row" name="table-row" class="form-select" required>
                                                                <option value="">Select Row</option>
                                                                <option value="1A">Row 1A</option>
                                                                <option value="1B">Row 1B</option>
                                                                <option value="1C">Row 1C</option>
                                                                <option value="1D">Row 1D</option>
                                                                <option value="1E">Row 1E</option>
                                                                <option value="1F">Row 1F</option>
                                                                <option value="1G">Row 1G</option>
                                                                <option value="1H">Row 1H</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-start" class="form-label">Start Number</label>
                                                            <input type="number" id="table-start" name="table-start" class="form-control" min="1" max="99" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="table-end" class="form-label">End Number</label>
                                                            <input type="number" id="table-end" name="table-end" class="form-control" min="1" max="99" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-x-start" class="form-label">X Position Start</label>
                                                            <input type="number" id="table-x-start" name="table-x-start" class="form-control" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-y-position" class="form-label">Y Position</label>
                                                            <input type="number" id="table-y-position" name="table-y-position" class="form-control" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-width" class="form-label">Table Width</label>
                                                            <input type="number" id="table-width" name="table-width" class="form-control" value="54" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-height" class="form-label">Table Height</label>
                                                            <input type="number" id="table-height" name="table-height" class="form-control" value="36" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-spacing" class="form-label">Horizontal Spacing</label>
                                                            <input type="number" id="table-spacing" name="table-spacing" class="form-control" value="57" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="table-status" class="form-label">Initial Status</label>
                                                            <select id="table-status" name="table-status" class="form-select" required>
                                                                <option value="active">Active</option>
                                                                <option value="disabled">Disabled</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-12">
                                                        <button type="submit" class="btn btn-primary">Add Tables</button>
                                                        <button type="button" id="preview-tables" class="btn btn-secondary">Preview</button>
                                                    </div>
                                                </div>
                                            </form>

                                            <div id="table-preview" class="mt-4" style="display: none;">
                                                <h5>Preview</h5>
                                                <div class="border p-3">
                                                    <svg id="preview-svg" width="1200" height="200" viewBox="0 0 1600 200">
                                                        <!-- Preview will be rendered here -->
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>

  <?php include_once("footer.php"); ?>

  <script>
    $(document).ready(function() {
        // Load table data on page load
        loadTableData();

        // Filter table data when filters change
        $('#table-row-filter, #table-status-filter').on('change', function() {
            loadTableData();
        });

        // Preview tables
        $('#preview-tables').on('click', function(e) {
            e.preventDefault();
            previewTables();
        });

        // Submit form to add tables
        $('#add-table-form').on('submit', function(e) {
            e.preventDefault();
            addTables();
        });

        // Function to load table data
        function loadTableData() {
            const rowFilter = $('#table-row-filter').val();
            const statusFilter = $('#table-status-filter').val();

            $.ajax({
                url: 'get_tables.php',
                type: 'GET',
                data: {
                    row: rowFilter,
                    status: statusFilter
                },
                dataType: 'json',
                success: function(data) {
                    renderTableData(data);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading table data:', error);
                    alert('Failed to load table data. Please try again.');
                }
            });
        }

        // Function to render table data
        function renderTableData(data) {
            const tableBody = $('#table-list tbody');
            tableBody.empty();

            if (data.length === 0) {
                tableBody.append('<tr><td colspan="4" class="text-center">No tables found</td></tr>');
                return;
            }

            data.forEach(function(table) {
                const row = $('<tr></tr>');
                row.append(`<td>${table.id}</td>`);
                row.append(`<td>${table.row}</td>`);

                const statusBadge = table.status === 'active'
                    ? '<span class="badge bg-success">Active</span>'
                    : '<span class="badge bg-danger">Disabled</span>';
                row.append(`<td>${statusBadge}</td>`);

                const toggleBtn = table.status === 'active'
                    ? `<button class="btn btn-sm btn-danger toggle-status" data-id="${table.id}" data-status="active">Disable</button>`
                    : `<button class="btn btn-sm btn-success toggle-status" data-id="${table.id}" data-status="disabled">Enable</button>`;

                row.append(`<td>
                    ${toggleBtn}
                    <button class="btn btn-sm btn-danger delete-table" data-id="${table.id}">Delete</button>
                </td>`);

                tableBody.append(row);
            });

            // Add event listeners for toggle and delete buttons
            $('.toggle-status').on('click', function() {
                const tableId = $(this).data('id');
                const currentStatus = $(this).data('status');
                const newStatus = currentStatus === 'active' ? 'disabled' : 'active';

                toggleTableStatus(tableId, newStatus);
            });

            $('.delete-table').on('click', function() {
                const tableId = $(this).data('id');
                if (confirm('Are you sure you want to delete this table?')) {
                    deleteTable(tableId);
                }
            });
        }

        // Function to toggle table status
        function toggleTableStatus(tableId, newStatus) {
            $.ajax({
                url: 'update_table_status.php',
                type: 'POST',
                data: {
                    id: tableId,
                    status: newStatus
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        loadTableData();
                    } else {
                        alert('Failed to update table status: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error updating table status:', error);
                    alert('Failed to update table status. Please try again.');
                }
            });
        }

        // Function to delete table
        function deleteTable(tableId) {
            $.ajax({
                url: 'delete_table.php',
                type: 'POST',
                data: {
                    id: tableId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        loadTableData();
                    } else {
                        alert('Failed to delete table: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting table:', error);
                    alert('Failed to delete table. Please try again.');
                }
            });
        }

        // Function to preview tables
        function previewTables() {
            const row = $('#table-row').val();
            const start = parseInt($('#table-start').val());
            const end = parseInt($('#table-end').val());
            const xStart = parseInt($('#table-x-start').val());
            const yPos = parseInt($('#table-y-position').val());
            const width = parseInt($('#table-width').val());
            const height = parseInt($('#table-height').val());
            const spacing = parseInt($('#table-spacing').val());

            // Validate inputs
            if (!row || isNaN(start) || isNaN(end) || isNaN(xStart) || isNaN(yPos) ||
                isNaN(width) || isNaN(height) || isNaN(spacing)) {
                alert('Please fill in all fields with valid numbers');
                return;
            }

            if (start > end) {
                alert('Start number must be less than or equal to end number');
                return;
            }

            // Clear previous preview
            const svg = $('#preview-svg');
            svg.empty();

            // Generate preview
            for (let i = start; i <= end; i++) {
                const tableId = `${row}${i.toString().padStart(2, '0')}`;
                const xPos = xStart + (i - start) * spacing;

                // Create table rectangle
                const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                rect.setAttribute('x', xPos);
                rect.setAttribute('y', yPos);
                rect.setAttribute('width', width);
                rect.setAttribute('height', height);
                rect.setAttribute('fill', '#539bff');
                rect.setAttribute('stroke', '#ffffff');
                rect.setAttribute('stroke-width', '2');
                rect.setAttribute('rx', '5');
                rect.setAttribute('ry', '5');

                // Create table text
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', xPos + width / 2);
                text.setAttribute('y', yPos + height / 2 + 5);
                text.setAttribute('font-size', '17');
                text.setAttribute('fill', '#ffffff');
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('font-weight', 'bold');
                text.textContent = tableId;

                // Add to SVG
                svg.append(rect);
                svg.append(text);
            }

            // Show preview
            $('#table-preview').show();
        }

        // Function to add tables
        function addTables() {
            const row = $('#table-row').val();
            const start = parseInt($('#table-start').val());
            const end = parseInt($('#table-end').val());
            const xStart = parseInt($('#table-x-start').val());
            const yPos = parseInt($('#table-y-position').val());
            const width = parseInt($('#table-width').val());
            const height = parseInt($('#table-height').val());
            const spacing = parseInt($('#table-spacing').val());
            const status = $('#table-status').val();

            // Validate inputs
            if (!row || isNaN(start) || isNaN(end) || isNaN(xStart) || isNaN(yPos) ||
                isNaN(width) || isNaN(height) || isNaN(spacing)) {
                alert('Please fill in all fields with valid numbers');
                return;
            }

            if (start > end) {
                alert('Start number must be less than or equal to end number');
                return;
            }

            // Prepare data for submission
            const tables = [];
            for (let i = start; i <= end; i++) {
                const tableId = `${row}${i.toString().padStart(2, '0')}`;
                const xPos = xStart + (i - start) * spacing;

                tables.push({
                    id: tableId,
                    row: row,
                    x: xPos,
                    y: yPos,
                    width: width,
                    height: height,
                    status: status
                });
            }

            // Submit data
            $.ajax({
                url: 'add_tables.php',
                type: 'POST',
                data: {
                    tables: JSON.stringify(tables)
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Tables added successfully!');
                        $('#add-table-form')[0].reset();
                        $('#table-preview').hide();
                        loadTableData();
                    } else {
                        alert('Failed to add tables: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error adding tables:', error);
                    alert('Failed to add tables. Please try again.');
                }
            });
        }
    });
  </script>
</body>
</html>
