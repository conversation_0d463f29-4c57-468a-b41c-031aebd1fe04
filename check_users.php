<?php
// Include database connection
include 'dbconnect/_dbconnect.php';

// Connect to database
$conn = db_connect();

echo "<h2>User Management - Password Recovery</h2>";

try {
    // Check if kp_login table exists
    $sql = "SHOW TABLES LIKE 'kp_login'";
    $result = $conn->query($sql);
    
    if ($result->rowCount() > 0) {
        echo "<h3>Login Table Structure:</h3>";
        
        // Get table structure
        $sql = "DESCRIBE kp_login";
        $result = $conn->query($sql);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Get all users (without showing passwords)
        $sql = "SELECT id, user, email, create_date FROM kp_login ORDER BY id";
        $result = $conn->query($sql);
        
        echo "<h3>Existing Users:</h3>";
        if ($result->rowCount() > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Created Date</th></tr>";
            
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . $row['user'] . "</td>";
                echo "<td>" . ($row['email'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['create_date'] ?: 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No users found in the database.</p>";
        }
        
        // Create/Reset admin user
        echo "<h3>Reset Admin User:</h3>";
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<p>Click the button below to create/reset the admin user with default credentials:</p>";
        echo "<p><strong>Username:</strong> admin<br>";
        echo "<strong>Password:</strong> admin123</p>";
        echo "<input type='submit' name='reset_admin' value='Create/Reset Admin User' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>";
        echo "</form>";
        
        // Handle form submission
        if (isset($_POST['reset_admin'])) {
            // Hash the password
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            
            // Check if admin user exists
            $checkSql = "SELECT id FROM kp_login WHERE user = 'admin'";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->execute();
            
            if ($checkStmt->rowCount() > 0) {
                // Update existing admin user
                $updateSql = "UPDATE kp_login SET pass = :password WHERE user = 'admin'";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bindParam(':password', $password);
                
                if ($updateStmt->execute()) {
                    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
                    echo "✅ Admin user password has been reset successfully!<br>";
                    echo "Username: <strong>admin</strong><br>";
                    echo "Password: <strong>admin123</strong>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
                    echo "❌ Failed to update admin user password.";
                    echo "</div>";
                }
            } else {
                // Create new admin user
                $insertSql = "INSERT INTO kp_login (user, pass, email, create_date) VALUES ('admin', :password, '<EMAIL>', NOW())";
                $insertStmt = $conn->prepare($insertSql);
                $insertStmt->bindParam(':password', $password);
                
                if ($insertStmt->execute()) {
                    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
                    echo "✅ Admin user has been created successfully!<br>";
                    echo "Username: <strong>admin</strong><br>";
                    echo "Password: <strong>admin123</strong>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
                    echo "❌ Failed to create admin user.";
                    echo "</div>";
                }
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Login table (kp_login) does not exist!</p>";
        
        // Create the login table
        echo "<h3>Creating Login Table:</h3>";
        $createTableSql = "CREATE TABLE kp_login (
            id INT(11) NOT NULL AUTO_INCREMENT,
            user VARCHAR(50) NOT NULL UNIQUE,
            pass VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            create_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        
        try {
            $conn->exec($createTableSql);
            echo "<p style='color: green;'>✅ Login table created successfully!</p>";
            
            // Create default admin user
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            $insertSql = "INSERT INTO kp_login (user, pass, email) VALUES ('admin', :password, '<EMAIL>')";
            $insertStmt = $conn->prepare($insertSql);
            $insertStmt->bindParam(':password', $password);
            
            if ($insertStmt->execute()) {
                echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
                echo "✅ Default admin user created successfully!<br>";
                echo "Username: <strong>admin</strong><br>";
                echo "Password: <strong>admin123</strong>";
                echo "</div>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Error creating table: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Close connection
db_close($conn);

echo "<hr>";
echo "<p><a href='login/' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Go to Login Page</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
}

table {
    background: white;
    width: 100%;
    max-width: 800px;
}

th {
    background: #007cba;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

tr:nth-child(even) {
    background: #f9f9f9;
}
</style>
