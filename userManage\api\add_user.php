<?php
session_start();
require_once '../../includes/role_check.php';
require_once '../../dbconnect/_dbconnect.php';

// Check if user is logged in and has admin access
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Validate required fields (password is no longer required as it will be set to default)
$required_fields = ['username', 'role', 'status'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => ucfirst($field) . ' is required']);
        exit;
    }
}

$username = trim($_POST['username']);
$password = 'pwd123'; // Set default password
$email = isset($_POST['email']) ? trim($_POST['email']) : null;
$name = isset($_POST['name']) ? trim($_POST['name']) : null;
$role = $_POST['role'];
$status = (int)$_POST['status'];

// Validate role permissions
if ($_SESSION['role'] !== 'super_admin' && in_array($role, ['super_admin', 'admin'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'You do not have permission to create admin users']);
    exit;
}

// Validate role values
$valid_roles = ['user'];
if ($_SESSION['role'] === 'super_admin') {
    $valid_roles = ['super_admin', 'admin', 'user'];
}

if (!in_array($role, $valid_roles)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid role specified']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();

    // Check if username already exists
    $stmt = $pdo->prepare("SELECT id FROM kp_login WHERE user = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Username already exists']);
        exit;
    }
    
    // Check if email already exists (if provided)
    if ($email) {
        $stmt = $pdo->prepare("SELECT id FROM kp_login WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            exit;
        }
    }
    
    // Hash password using Argon2id
    $hashedPassword = password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3,
    ]);
    
    // Insert new user with first_login flag set to 1 (requires password change)
    $stmt = $pdo->prepare("INSERT INTO kp_login (user, pass, email, name, role, status, first_login, created_at) VALUES (?, ?, ?, ?, ?, ?, 1, NOW())");
    $result = $stmt->execute([$username, $hashedPassword, $email, $name, $role, $status]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo json_encode([
            'success' => true,
            'message' => 'User created successfully with default password. User will be required to change password on first login.',
            'user_id' => $userId,
            'default_password' => 'pwd123'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to create user']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
