<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="../assets/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="../assets/css/styles.css" />

  <title>Table Setup</title>

  <!-- Custom CSS for table display -->
  <style>
    /* Custom badge colors */
    .badge.bg-purple {
      background-color: #6f42c1 !important;
      color: white;
    }

    .badge.bg-pink {
      background-color: #e83e8c !important;
      color: white;
    }

    /* Table preview styling */
    #table-preview {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      background-color: #f8f9fa;
    }

    #preview-svg rect {
      transition: all 0.3s ease;
    }

    #preview-svg rect:hover {
      opacity: 0.8;
      stroke-width: 3;
    }

    /* Table status indicators */
    .table-status-active {
      color: #28a745;
    }

    .table-status-disabled {
      color: #dc3545;
    }

    /* Disabled until date styling */
    td small.text-muted {
      font-size: 0.75rem;
      display: block;
      margin-top: 0.25rem;
    }

    /* Row color indicators */
    .row-color {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 3px;
      margin-right: 5px;
      vertical-align: middle;
    }

    /* Row color indicators - consistent with floorplan */
    .row-1A { background-color: #28a745; } /* Green */
    .row-1B { background-color: #ffc107; } /* Yellow */
    .row-1C { background-color: #dc3545; } /* Red */
    .row-1D { background-color: #17a2b8; } /* Info/Cyan */
    .row-1E { background-color: #6f42c1; } /* Purple */
    .row-1F { background-color: #fd7e14; } /* Orange */
    .row-1G { background-color: #20c997; } /* Teal */
    .row-1H { background-color: #e83e8c; } /* Pink */

    /* Alternative format support */
    .row-A { background-color: #28a745; } /* Green */
    .row-B { background-color: #ffc107; } /* Yellow */
    .row-C { background-color: #dc3545; } /* Red */
    .row-D { background-color: #17a2b8; } /* Info/Cyan */
    .row-E { background-color: #6f42c1; } /* Purple */
    .row-F { background-color: #fd7e14; } /* Orange */
    .row-G { background-color: #20c997; } /* Teal */
    .row-H { background-color: #e83e8c; } /* Pink */
    .row-V { background-color: #ffaa00; } /* VIP Yellow */
    .row-P { background-color: #6f42c1; } /* Premium Purple */

    /* Loading Overlay */
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      border-radius: 8px;
    }

    .spinner-container {
      text-align: center;
    }

    /* Date picker styling */
    .datepicker {
      position: relative;
    }

    .datepicker .form-control {
      background-color: white;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }

    .datepicker .input-group-text {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-left: none;
      border-radius: 0 6px 6px 0;
      cursor: pointer;
    }

    /* Card improvements */
    .card {
      position: relative;
      border: none;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    }

    .card-body {
      padding: 1.5rem;
    }

    /* Tab improvements */
    .nav-tabs .nav-link {
      border: none;
      border-radius: 8px;
      margin-right: 0.5rem;
      padding: 0.75rem 1.25rem;
      color: #6c757d;
      background-color: #f8f9fa;
      transition: all 0.2s ease;
    }

    .nav-tabs .nav-link.active {
      background-color: #5d87ff;
      color: white;
      box-shadow: 0 2px 8px rgba(93, 135, 255, 0.3);
    }

    .nav-tabs .nav-link:hover:not(.active) {
      background-color: #e9ecef;
      color: #495057;
    }
  </style>

</head>
