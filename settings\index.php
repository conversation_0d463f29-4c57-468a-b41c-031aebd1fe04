<?php
/**
 * System Settings Page
 * 
 * This page allows administrators to configure system settings
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/User.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Check if user has admin role
if (Session::get('role') !== 'admin') {
    header('Location: ../index.php?error=unauthorized');
    exit;
}

// Generate CSRF token
$csrf_token = Session::generateCsrfToken();

// Include header
$pageTitle = "System Settings";
include_once '../includes/header.php';

// Get current settings
$settingsFile = dirname(__DIR__) . '/config/settings.json';
$settings = [];

if (file_exists($settingsFile)) {
    $settings = json_decode(file_get_contents($settingsFile), true);
}

// Set default values if not set
$settings['company_name'] = $settings['company_name'] ?? 'Sawasdee Restaurant';
$settings['company_address'] = $settings['company_address'] ?? '';
$settings['company_phone'] = $settings['company_phone'] ?? '';
$settings['company_email'] = $settings['company_email'] ?? '';
$settings['booking_start_time'] = $settings['booking_start_time'] ?? '10:00';
$settings['booking_end_time'] = $settings['booking_end_time'] ?? '22:00';
$settings['booking_interval'] = $settings['booking_interval'] ?? 30;
$settings['max_days_advance'] = $settings['max_days_advance'] ?? 30;
$settings['default_cruise_id'] = $settings['default_cruise_id'] ?? 1;
$settings['enable_notifications'] = $settings['enable_notifications'] ?? true;
$settings['maintenance_mode'] = $settings['maintenance_mode'] ?? false;
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">System Settings</h4>
        </div>
        <div class="card-body">
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    Settings updated successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    $message = '';
                    switch ($_GET['error']) {
                        case 'invalid_token':
                            $message = 'Invalid security token. Please try again.';
                            break;
                        case 'write_error':
                            $message = 'Could not write settings file. Please check file permissions.';
                            break;
                        default:
                            $message = 'An error occurred while updating settings.';
                    }
                    echo $message;
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form id="settingsForm" action="save_settings.php" method="post" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">Company Information</h5>
                        
                        <div class="mb-3">
                            <label for="company_name" class="form-label">Company Name</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo $settings['company_name']; ?>" required>
                            <div class="invalid-feedback">Please enter the company name.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_address" class="form-label">Address</label>
                            <textarea class="form-control" id="company_address" name="company_address" rows="3"><?php echo $settings['company_address']; ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_phone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="company_phone" name="company_phone" value="<?php echo $settings['company_phone']; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="company_email" name="company_email" value="<?php echo $settings['company_email']; ?>">
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="mb-3">Booking Settings</h5>
                        
                        <div class="mb-3">
                            <label for="booking_start_time" class="form-label">Booking Start Time</label>
                            <input type="time" class="form-control" id="booking_start_time" name="booking_start_time" value="<?php echo $settings['booking_start_time']; ?>" required>
                            <div class="invalid-feedback">Please enter a valid start time.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="booking_end_time" class="form-label">Booking End Time</label>
                            <input type="time" class="form-control" id="booking_end_time" name="booking_end_time" value="<?php echo $settings['booking_end_time']; ?>" required>
                            <div class="invalid-feedback">Please enter a valid end time.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="booking_interval" class="form-label">Booking Interval (minutes)</label>
                            <select class="form-select" id="booking_interval" name="booking_interval" required>
                                <option value="15" <?php echo $settings['booking_interval'] == 15 ? 'selected' : ''; ?>>15 minutes</option>
                                <option value="30" <?php echo $settings['booking_interval'] == 30 ? 'selected' : ''; ?>>30 minutes</option>
                                <option value="60" <?php echo $settings['booking_interval'] == 60 ? 'selected' : ''; ?>>1 hour</option>
                            </select>
                            <div class="invalid-feedback">Please select a booking interval.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="max_days_advance" class="form-label">Maximum Days in Advance</label>
                            <input type="number" class="form-control" id="max_days_advance" name="max_days_advance" value="<?php echo $settings['max_days_advance']; ?>" min="1" max="365" required>
                            <div class="invalid-feedback">Please enter a valid number of days (1-365).</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="default_cruise_id" class="form-label">Default Cruise ID</label>
                            <input type="number" class="form-control" id="default_cruise_id" name="default_cruise_id" value="<?php echo $settings['default_cruise_id']; ?>" min="1" required>
                            <div class="invalid-feedback">Please enter a valid cruise ID.</div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5 class="mb-3">System Settings</h5>
                        
                        <div class="mb-3 form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" <?php echo $settings['enable_notifications'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="enable_notifications">Enable Notifications</label>
                        </div>
                        
                        <div class="mb-3 form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="maintenance_mode">Maintenance Mode</label>
                            <div class="form-text">When enabled, only administrators can access the system.</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="mb-3">Database Maintenance</h5>
                        
                        <div class="mb-3">
                            <button type="button" id="backupDatabase" class="btn btn-primary">Backup Database</button>
                            <div class="form-text">Creates a backup of the database.</div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" id="optimizeDatabase" class="btn btn-info">Optimize Database</button>
                            <div class="form-text">Optimizes database tables for better performance.</div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" id="clearCache" class="btn btn-warning">Clear Cache</button>
                            <div class="form-text">Clears system cache files.</div>
                        </div>
                    </div>
                </div>
                
                <div class="text-end mt-4">
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('settingsForm');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Database backup
    document.getElementById('backupDatabase').addEventListener('click', function() {
        Swal.fire({
            title: 'Backup Database',
            text: 'Are you sure you want to create a database backup?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, backup now',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Creating Backup',
                    text: 'Please wait while the backup is being created...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // Send AJAX request
                $.ajax({
                    url: 'database_actions.php',
                    type: 'POST',
                    data: {
                        action: 'backup',
                        csrf_token: '<?php echo $csrf_token; ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Backup Complete',
                                text: response.message,
                                icon: 'success'
                            });
                        } else {
                            Swal.fire({
                                title: 'Backup Failed',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Backup Failed',
                            text: 'An error occurred while creating the backup.',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
    
    // Database optimization
    document.getElementById('optimizeDatabase').addEventListener('click', function() {
        Swal.fire({
            title: 'Optimize Database',
            text: 'Are you sure you want to optimize the database?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, optimize now',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Optimizing Database',
                    text: 'Please wait while the database is being optimized...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // Send AJAX request
                $.ajax({
                    url: 'database_actions.php',
                    type: 'POST',
                    data: {
                        action: 'optimize',
                        csrf_token: '<?php echo $csrf_token; ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Optimization Complete',
                                text: response.message,
                                icon: 'success'
                            });
                        } else {
                            Swal.fire({
                                title: 'Optimization Failed',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Optimization Failed',
                            text: 'An error occurred while optimizing the database.',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
    
    // Clear cache
    document.getElementById('clearCache').addEventListener('click', function() {
        Swal.fire({
            title: 'Clear Cache',
            text: 'Are you sure you want to clear the system cache?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, clear now',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                Swal.fire({
                    title: 'Clearing Cache',
                    text: 'Please wait while the cache is being cleared...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // Send AJAX request
                $.ajax({
                    url: 'database_actions.php',
                    type: 'POST',
                    data: {
                        action: 'clear_cache',
                        csrf_token: '<?php echo $csrf_token; ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Cache Cleared',
                                text: response.message,
                                icon: 'success'
                            });
                        } else {
                            Swal.fire({
                                title: 'Cache Clearing Failed',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Cache Clearing Failed',
                            text: 'An error occurred while clearing the cache.',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
