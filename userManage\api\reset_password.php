<?php
header('Content-Type: application/json');
require_once '../../dbconnect/_dbconnect.php';

// Check if user is logged in and has admin privileges
session_start();
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if user has admin or super admin role
if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], ['admin', 'super_admin'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Insufficient privileges']);
    exit;
}

// Validate required fields
if (!isset($_POST['id']) || empty($_POST['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

$userId = (int)$_POST['id'];
$defaultPassword = 'pwd123'; // Default password

try {
    $pdo = db_connect();
    
    // Check if user exists
    $checkStmt = $pdo->prepare("SELECT id, user FROM kp_login WHERE id = ?");
    $checkStmt->execute([$userId]);
    $user = $checkStmt->fetch();
    
    if (!$user) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Prevent admin from resetting their own password
    if ($userId == $_SESSION['id']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'You cannot reset your own password']);
        exit;
    }
    
    // Hash the default password
    $hashedPassword = password_hash($defaultPassword, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3,
    ]);
    
    // Update user password and set first_login = 1
    $stmt = $pdo->prepare("UPDATE kp_login SET pass = ?, first_login = 1 WHERE id = ?");
    $result = $stmt->execute([$hashedPassword, $userId]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Password has been reset successfully. User will be required to change password on next login.',
            'default_password' => $defaultPassword,
            'username' => $user['user']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to reset password']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in reset_password.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in reset_password.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An error occurred while resetting password']);
}
?>
