<?php
// set timezone to Asia/Bangkok
date_default_timezone_set('Asia/Bangkok');

// Connect to database
function db_connect()
{
    /* Server */
    // $servername = "localhost";
    // $username = "mrkp";
    // $password = "FdFdhk0123#@!";
    // $dbname = "kp_service";
    // $char ='utf8';
    // $port = 3306;
    // $dsn = "mysql:host=$servername;dbname=$dbname;charset=$char;port=$port";

    /* Localhost */
    $servername = "localhost";
    $username = "root";
    $password = "kokoku123#@!";
    $dbname = "kp_service";
    $char ='utf8';
    $port = 3306;
    $dsn = "mysql:host=$servername;dbname=$dbname;charset=$char;port=$port";

    $options = array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false );

    try
    {
        $con = new PDO($dsn,$username,$password,$options);
        //$con = new mysqli($constring);
        if ($con) {
            return $con;
        }
    }
    catch (PDOException $e)
    {
        echo $e->getMessage();
        return $e;
    }
}

// Close
function db_close($con)
{
    $con = null;
}



// function get cruise id by user key and return cruise_id, cruise_name, cruise_key as json
function get_cruise_by_user_key($userKey){
    $conn = db_connect();

    // Use prepared statement to prevent SQL injection
    $sql = "SELECT * FROM kp_booking_cruise WHERE user_key = :userKey";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':userKey', $userKey, PDO::PARAM_STR);
    $stmt->execute();

    $num = $stmt->rowCount();
    if($num == 1){
        $row = $stmt->fetch();
        $cruise_id = $row['id'];
        $cruise_name = $row['cruise_name'];
        $cruise_key = $row['cruise_key'];
        $cruise = array('cruise_id' => $cruise_id, 'cruise_name' => $cruise_name, 'cruise_key' => $cruise_key);
        return json_encode($cruise);
    }
    else{
        return json_encode(array('error' => 'No cruise found for this user'));
    }
}


/**
 * Check if a password is stored in plain text
 * This function checks if a password is stored in plain text and needs to be hashed
 *
 * @param string $stored_password The password stored in the database
 * @param string $plain_password The plain text password to check against
 * @return bool True if the password is in plain text, false otherwise
 */
function is_plain_text_password($stored_password, $plain_password) {
    // If the stored password is identical to the plain password, it's not hashed
    return $stored_password === $plain_password;
}

/**
 * Update a user's password to use secure hashing
 *
 * @param int $user_id The user ID
 * @param string $plain_password The plain text password to hash
 * @return bool True if the password was updated successfully, false otherwise
 */
function update_password_hash($user_id, $plain_password) {
    try {
        $conn = db_connect();

        // First, check if the pass column is large enough for secure hashes
        $sql = "SELECT CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'kp_login'
                AND COLUMN_NAME = 'pass'";

        $stmt = $conn->query($sql);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If column is too small, log an error and return false
        if ($result && $result['CHARACTER_MAXIMUM_LENGTH'] < 100) {
            error_log("Password column in kp_login table is too small (" . $result['CHARACTER_MAXIMUM_LENGTH'] .
                     " chars). Please run dbconnect/update_password_column.php to fix this issue.");
            return false;
        }

        // Hash the password using Argon2id (more secure than bcrypt)
        $hashed_password = password_hash($plain_password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);

        // Update the password in the database
        $sql = "UPDATE kp_login SET pass = :password, update_date = NOW() WHERE id = :user_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':password', $hashed_password, PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);

        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Failed to update password hash: " . $e->getMessage());
        return false;
    }
}

/**
 * Check login credentials and create a session if valid
 *
 * @param string $username The username to check
 * @param string $password The password to check
 * @return string "success" if login is successful, error message otherwise
 */
function check_login($username, $password) {
    try {
        $conn = db_connect();

        // Use prepared statement to prevent SQL injection
        $sql = "SELECT * FROM kp_login WHERE user = :username";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();

        // Check if user exists
        if ($stmt->rowCount() != 1) {
            // For security, use the same error message for all failures
            error_log("User not found: {$username}");
            return "Invalid Credentials";
        }

        $row = $stmt->fetch();

        // Check if account is active
        if ($row['status'] != 1) {
            error_log("Account inactive for user: {$username}");
            return "Account Inactive";
        }

        // Log the stored password for debugging
        error_log("Stored password for {$username}: " . substr($row['pass'], 0, 5) . '...');
        error_log("Password algorithm info: " . json_encode(password_get_info($row['pass'])));

        $authenticated = false;

        // Check if the password is hashed
        $password_info = password_get_info($row['pass']);
        error_log("Password info: " . json_encode($password_info));

        // First try plain text comparison
        error_log("Trying plain text password comparison");
        error_log("Stored password: {$row['pass']}");
        error_log("Input password: {$password}");

        // Check if the stored password matches the provided password
        $authenticated = ($row['pass'] === $password);
        error_log("Plain text comparison result: " . ($authenticated ? 'true' : 'false'));

        // If plain text authentication succeeded, upgrade to hashed password
        if ($authenticated) {
            error_log("Upgrading plain text password to hash for user: {$username}");
            update_password_hash($row['id'], $password);
        } else if ($password_info['algo'] !== 0) {
            // If plain text failed and it looks like a hash, try password_verify
            error_log("Plain text failed, trying password_verify for hashed password");
            $authenticated = password_verify($password, $row['pass']);
            error_log("password_verify result: " . ($authenticated ? 'true' : 'false'));

            // Check if the password hash needs to be rehashed
            if ($authenticated && password_needs_rehash($row['pass'], PASSWORD_ARGON2ID)) {
                error_log("Rehashing password for user: {$username}");
                update_password_hash($row['id'], $password);
            }
        }

        if ($authenticated) {
            // Start session if not already started
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Regenerate session ID to prevent session fixation attacks
            session_regenerate_id(true);

            // Set session variables
            $_SESSION['loggedin'] = true;
            $_SESSION['username'] = $username;
            $_SESSION['role'] = $row['role'];
            $_SESSION['id'] = $row['id'];
            $_SESSION['name'] = $row['name'];
            $_SESSION['email'] = $row['email'];
            $_SESSION['userKey'] = $row['user_key'];
            $_SESSION['userStatus'] = $row['status'];
            $_SESSION['cruiseId'] = get_cruise_by_user_key($row['user_key']);
            $_SESSION['first_login'] = isset($row['first_login']) ? $row['first_login'] : 0; // Track if user needs to change password

            // Set last activity time for session timeout
            $_SESSION['last_activity'] = time();

            // Log successful login
            $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Unknown';
            error_log("Successful login: User {$username} from IP {$ip}");

            // Check if user needs to change password on first login
            if (isset($row['first_login']) && $row['first_login'] == 1) {
                return "first_login_required";
            }

            // Return success
            return "success";
        }

        // Log failed login attempt
        $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Unknown';
        error_log("Failed login attempt: User {$username} from IP {$ip}");

        return "Invalid Credentials";
    } catch (PDOException $e) {
        error_log("Database error during login: " . $e->getMessage());
        return "System Error";
    }
}


// function get zone by cruise id
function get_zone_by_cruise_id($cruiseId){
    $conn = db_connect();

    // Use prepared statement to prevent SQL injection
    $sql = "SELECT * FROM kp_booking_zones WHERE cruise_id = :cruiseId ORDER BY q ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':cruiseId', $cruiseId, PDO::PARAM_INT);
    $stmt->execute();

    $num = $stmt->rowCount();
    if($num > 0){
        $zones = array();
        while($row = $stmt->fetch()){
            $zone = array('zone_id' => $row['id'], 'zone_name' => $row['name']);
            array_push($zones, $zone);
        }
        return json_encode($zones);
    }
    else{
        return json_encode(array('error' => 'No zones found for this cruise'));
    }
}

// function get occupancy_data
function get_occupancy_data($cruiseId, $zoneId, $date){
    $conn = db_connect();

    // Use prepared statement to prevent SQL injection
    $sql = "SELECT * FROM kp_booking_occupancy WHERE cruise_id = :cruiseId AND zone_id = :zoneId AND date = :date";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':cruiseId', $cruiseId, PDO::PARAM_INT);
    $stmt->bindParam(':zoneId', $zoneId, PDO::PARAM_INT);
    $stmt->bindParam(':date', $date, PDO::PARAM_STR);
    $stmt->execute();

    $num = $stmt->rowCount();
    if($num == 1){
        $row = $stmt->fetch();
        $occupancy = array(
            'date' => $row['date'],
            'zone_id' => $row['zone_id'],
            'zone_name' => $row['zone_name'],
            'occupancy' => $row['occupancy'],
            'max_occupancy' => $row['max_occupancy'],
            'bar_color' => $row['bar_color']
        );
        return json_encode($occupancy);
    }
    else{
        return json_encode(array('error' => 'No occupancy data found'));
    }
}

// function generate order no
function generate_order_no() {
    $conn = db_connect();
    $year = date('Y');
    $month = date('m');
    $prefix = "SWD{$year}{$month}";

    // Query to find the highest orderNo this month using prepared statement
    $sql = "SELECT orderNo FROM kp_booking WHERE orderNo LIKE :prefix ORDER BY orderNo DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $searchPattern = $prefix . '%';
    $stmt->bindParam(':prefix', $searchPattern, PDO::PARAM_STR);
    $stmt->execute();

    $num = $stmt->rowCount();
    if ($num == 0) {
        $newOrderNo = $prefix . "0001";
    } else {
        $row = $stmt->fetch();
        $lastOrderNo = $row['orderNo'];
        $lastOrderNo = substr($lastOrderNo, -4);
        $newOrderNo = $prefix . str_pad($lastOrderNo + 1, 4, '0', STR_PAD_LEFT);
    }

    return $newOrderNo;
}

function insert_booking($name, $phone, $adult, $child, $infant, $guide, $inspection, $teamLeader, $useDate, $useZone, $voucher, $agent, $remark, $cruiseId, $zoneId, $tables, $amount, $paymentType, $userKey, $paymentStatus = 'WP', $specialRequest = 0, $specialRequestNote = '') {

    file_put_contents("log_insert.txt", "Booking inserted" . PHP_EOL, FILE_APPEND);

    // Log parameters for debugging
    $debug_info = "Parameters: " . json_encode([
        'name' => $name,
        'phone' => $phone,
        'useDate' => $useDate,
        'useZone' => $useZone,
        'tables' => $tables
    ]);
    error_log($debug_info);
    file_put_contents("log_insert.txt", $debug_info . PHP_EOL, FILE_APPEND);

    // Validate required parameters
    if (empty($name) || empty($phone) || empty($useDate) || empty($useZone)) {
        $missing = [];
        if (empty($name)) $missing[] = 'name';
        if (empty($phone)) $missing[] = 'phone';
        if (empty($useDate)) $missing[] = 'useDate';
        if (empty($useZone)) $missing[] = 'useZone';

        $error_msg = "Missing required fields for booking: " . implode(', ', $missing);
        error_log($error_msg);
        file_put_contents("log_insert_error.txt", $error_msg . "\n", FILE_APPEND);
        throw new Exception($error_msg);
    }

    $conn = db_connect();
    $orderNo = generate_order_no();

    try {
        // Use prepared statement to prevent SQL injection
        $sql = "INSERT INTO kp_booking (orderNo, name, phone, adult, child, infant, guide, inspection, team_leader, use_date, use_zone, voucher, agent, remark, special_request, special_request_note, cruise_id, zone_id, tables, amount, pay_type, user_key, create_date, payment_status)
               VALUES (:orderNo, :name, :phone, :adult, :child, :infant, :guide, :inspection, :teamLeader, :useDate, :useZone, :voucher, :agent, :remark, :specialRequest, :specialRequestNote, :cruiseId, :zoneId, :tables, :amount, :payType, :userKey, NOW(), 'WP')";

        $stmt = $conn->prepare($sql);

        // Bind parameters with appropriate types
        $stmt->bindParam(':orderNo', $orderNo, PDO::PARAM_STR);
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':adult', $adult, PDO::PARAM_INT);
        $stmt->bindParam(':child', $child, PDO::PARAM_INT);
        $stmt->bindParam(':infant', $infant, PDO::PARAM_INT);
        $stmt->bindParam(':guide', $guide, PDO::PARAM_INT);
        $stmt->bindParam(':inspection', $inspection, PDO::PARAM_INT);
        $stmt->bindParam(':teamLeader', $teamLeader, PDO::PARAM_STR);
        $stmt->bindParam(':useDate', $useDate, PDO::PARAM_STR);
        $stmt->bindParam(':useZone', $useZone, PDO::PARAM_STR);
        $stmt->bindParam(':voucher', $voucher, PDO::PARAM_STR);
        $stmt->bindParam(':agent', $agent, PDO::PARAM_STR);
        $stmt->bindParam(':remark', $remark, PDO::PARAM_STR);
        $stmt->bindParam(':specialRequest', $specialRequest, PDO::PARAM_INT);
        $stmt->bindParam(':specialRequestNote', $specialRequestNote, PDO::PARAM_STR);
        $stmt->bindParam(':cruiseId', $cruiseId, PDO::PARAM_INT);
        $stmt->bindParam(':zoneId', $zoneId, PDO::PARAM_INT);
        $stmt->bindParam(':tables', $tables, PDO::PARAM_STR);
        $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
        $stmt->bindParam(':payType', $paymentType, PDO::PARAM_STR);
        $stmt->bindParam(':userKey', $userKey, PDO::PARAM_STR);
        // $stmt->bindParam(':paymentStatus', 'WP', PDO::PARAM_STR);


        // Log the SQL query and parameters for debugging
        $log_sql = "INSERT INTO kp_booking VALUES ('$orderNo', '$name', '$phone', $adult, $child, $infant, $guide, $inspection, '$teamLeader', '$useDate', '$useZone', '$voucher', '$agent', '$remark', $specialRequest, '$specialRequestNote', $cruiseId, $zoneId, '$tables', $amount, '$paymentType', '$userKey', NOW(), 'WP')";
        file_put_contents("log_insert_sql.txt", $log_sql . "\n", FILE_APPEND);

        $stmt->execute();

        // Log successful insertion
        file_put_contents("log_insert_success.txt", "Booking inserted successfully: Order #{$orderNo}\n", FILE_APPEND);

        // Create JSON files after booking is saved
        try {
            $date_result = create_json_file_by_date($useDate);
            error_log("create_json_file_by_date result: " . ($date_result ? 'success' : 'failed'));

            $zone_result = create_json_file_by_zone($useDate);
            error_log("create_json_file_by_zone result: " . ($zone_result ? 'success' : 'failed'));
        } catch (Exception $json_ex) {
            error_log("Error creating JSON files: " . $json_ex->getMessage());
            file_put_contents("log_insert_json_error.txt", "Error creating JSON files: " . $json_ex->getMessage() . "\n", FILE_APPEND);
            // Don't throw the exception - we still want to return the orderNo
        }

        return $orderNo;
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        $error_msg = "Database error while inserting booking: " . $e->getMessage();
        error_log($error_msg);
        file_put_contents("log_insert_error.txt", $error_msg . "\n", FILE_APPEND);
        throw new Exception("Failed to insert booking. Please try again later.");
    }
}

// function create json file id,zone, t_max, p_max
function create_json_file_by_zone($useDate) {
    // create folder by month
    $month = date('m', strtotime($useDate));
    $year = date('Y', strtotime($useDate));

    // Use absolute path
    $base_path = dirname(__DIR__) . '/floorplan/';
    $folder = $base_path . 'json/' . $year . '/' . $month;

    // Log the folder path for debugging
    error_log("Creating JSON folder: {$folder}");

    if (!file_exists($folder)) {
        if (!mkdir($folder, 0777, true)) {
            error_log("Failed to create directory: {$folder}");
            return false;
        }
    }

    $filename = $folder.'/json_by_zone.json';

    $conn = db_connect();

    $sql = "SELECT id, zone_name, t_max, p_max FROM kp_booking_zones";
    $result = $conn->query($sql);
    $num = $result->rowCount();
    if ($num > 0) {
        $zones = array();
        while ($row = $result->fetch()) {
            // $zone = array('zone_id' => $row['id'], 'zone_name' => $row['zone_name'], 't_max' => $row['t_max'], 'p_max' => $row['p_max']);
            $zone = array('zone_id' => $row['id'], 'zone_name' => $row['zone_name']);
            array_push($zones, $zone);
        }
        $json = json_encode($zones, JSON_PRETTY_PRINT);
        if (file_put_contents($filename, $json) === false) {
            error_log("Failed to write to file: {$filename}");
            return false;
        }
        return true;
    } else {
        $json = json_encode(array(), JSON_PRETTY_PRINT);
        if (file_put_contents($filename, $json) === false) {
            error_log("Failed to write to file: {$filename}");
            return false;
        }
        return true;
    }
}

// function create json file by date
function create_json_file_by_date($useDate) {
    // create folder by month
    $month = date('m', strtotime($useDate));
    $year = date('Y', strtotime($useDate));

    // Use absolute path
    $base_path = dirname(__DIR__) . '/floorplan/';
    $folder = $base_path . 'json/' . $year . '/' . $month;

    // Log the folder path for debugging
    error_log("Creating JSON folder for date: {$folder}");

    if (!file_exists($folder)) {
        if (!mkdir($folder, 0777, true)) {
            error_log("Failed to create directory for date: {$folder}");
            return false;
        }
    }

    // change date format to dd-mm-yyyy
    $useDateFile = date('d', strtotime($useDate));
    $filename = $folder.'/jsonBookingBy_' . $useDateFile . '.json';
    $conn = db_connect();
    $sql = "SELECT * FROM kp_booking WHERE use_date = '$useDate'";
    $result = $conn->query($sql);
    $num = $result->rowCount();
    if ($num > 0) {
        $bookings = array();
        while ($row = $result->fetch()) {
            $booking = array(
                'Order No' => $row['orderNo'],
                'BookID' => $row['booking_id'],
                'ZoneID' => $row['zone_id'],
                'Use Date' => $row['use_date'],
                'Amount' => $row['amount'],
                'Create Date' => $row['create_date'],
                'Customer' => $row['name'],
                'Phone' => $row['phone'],
                'Guests' => array(
                    'Adults' => $row['adult'],
                    'Children' => $row['child'],
                    'Infants' => $row['infant']
                ),
                'Guide' => $row['guide'],
                'FOC' => $row['inspection'],
                'TL' => $row['team_leader'],
                'Voucher No' => $row['voucher'],
                'Agent' => $row['agent'],
                'PaymentType' => $row['pay_type'],
                'PaymentStatus' => $row['payment_status'],
                'Request' => $row['remark'],
                'Special' => $row['special_request'],
                'SpecialNote' => $row['special_request_note'],
                'CruiseID' => $row['cruise_id'],
                'Floor' => isset($row['use_zone']) ? $row['use_zone'] : '',
                'Table' => isset($row['tables']) ? $row['tables'] : ''
            );
            array_push($bookings, $booking);
        }
        $json = json_encode($bookings, JSON_PRETTY_PRINT);
        if (file_put_contents($filename, $json) === false) {
            error_log("Failed to write to file: {$filename}");
            return false;
        }
        return true;
    } else {
        $json = json_encode(array(), JSON_PRETTY_PRINT);
        if (file_put_contents($filename, $json) === false) {
            error_log("Failed to write to file: {$filename}");
            return false;
        }
        return true;
    }

}


// function get_max_by_zone(){
//     $conn = db_connect();
//     $sql = "SELECT id, zone_name, t_max, p_max FROM kp_booking_zones";
//     $result = $conn->query($sql);
//     $num = $result->rowCount();
//     // print_r($num);
//     // exit();
//     if($num > 0){
//         $zones = array();
//         while($row = $result->fetch()){
//             $zone = array('zone_id' => $row['id'], 'zone_name' => $row['zone_name'], 't_max' => $row['t_max'], 'p_max' => $row['p_max']);
//             array_push($zones, $zone);
//         }
//         return json_encode($zones);
//     }
//     else{
//         return "Invalid Credentials";
//     }
// }



function generate_json_by_month($year, $month) {
    $conn = db_connect();
    $json = array();
    $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
    for ($i = 1; $i <= $days; $i++) {
        $thisdate = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($i, 2, '0', STR_PAD_LEFT);
        // $thisdate = '2025-03-01';

        $json[$thisdate] = array(
            'Date' => $thisdate,
            'Weekday' => date('l', strtotime($thisdate)),
            'DayofWeek' => date('N', strtotime($thisdate))
        );


        $sql = "
        SELECT
            z.id,
            z.zone_name,
            z.t_max,
            z.p_max
        FROM
            kp_booking_zones z
        ";
        $result = $conn->query($sql);
        $num = $result->rowCount();

        $rooftopE = 0;
        $rooftopM = 0;
        $prowE = 0;
        $prowM = 0;
        $fl2E = 0;
        $fl2M = 0;
        $fl1E = 0;
        $fl1M = 0;
        $vip1 = 0;
        $vip2 = 0;
        $vip3 = 0;
        $vip4 = 0;
        $sumall = 0;

        if ($num > 0) {

            while ($row = $result->fetch()) {
                $zone_id = $row['id'];
                $t_max = $row['t_max'];
                $p_max = $row['p_max'];

                $sql2 = "
                SELECT
                    sum(b.adult) as adult,
                    sum(b.child) as child,
                    sum(b.infant) as infant,
                    sum(b.guide) as guide,
                    sum(b.inspection) as inspection,
                    sum(b.team_leader) as team_leader,
                    count(b.orderNo) as count_t_value
                FROM
                    kp_service.kp_booking b
                WHERE
                    b.use_date = '$thisdate'
                AND
                    b.zone_id = '$zone_id'
                ";

                $result2 = $conn->query($sql2);
                $num2 = $result2->rowCount();

                while ($row2 = $result2->fetch()) {
                    $adult = $row2['adult'];
                    $child = $row2['child'];
                    $infant = $row2['infant'];
                    $guide = $row2['guide'];
                    $inspection = $row2['inspection'];
                    $team_leader = $row2['team_leader'];
                    $t_value = $row2['count_t_value'];

                    // $t_value = $adult + $child + $infant + $guide + $inspection + $team_leader;
                    $p_value = $adult + $child + $infant + $guide + $inspection + $team_leader;
                }

                if($zone_id==1){
                    $json[$thisdate]['RooftopE'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==2){
                    $json[$thisdate]['RooftopM'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==3){
                    $json[$thisdate]['ProwE'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==4){
                    $json[$thisdate]['ProwM'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==5){
                    $json[$thisdate]['Fl2E'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==6){
                    $json[$thisdate]['Fl2M'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==7){
                    $json[$thisdate]['Fl1E'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==8){
                    $json[$thisdate]['Fl1M'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==9){
                    $json[$thisdate]['VIP1'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==10){
                    $json[$thisdate]['VIP2'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==11){
                    $json[$thisdate]['VIP3'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }elseif($zone_id==12){
                    $json[$thisdate]['VIP4'] = array(
                        't_Value' => $t_value,
                        't_maxValue' => $t_max,
                        'p_Value' => $p_value,
                        'p_maxValue' => $p_max
                    );
                }
            }
        }


    }
    // creste json json_by_month.json
    $json_filename = 'json_by_month.json';
    $json = json_encode($json, JSON_PRETTY_PRINT);
    file_put_contents($json_filename, $json);
    return $json_filename;

}


/**
 * Book an entire floor
 *
 * @param string $name Customer name
 * @param string $phone Customer phone
 * @param string $useDate Date in YYYY-MM-DD format
 * @param string $useZone Floor/zone number
 * @param string $voucher Voucher code (optional)
 * @param string $agent Agent name (optional)
 * @param string $remark Booking remark (optional)
 * @param int $amount Booking amount (optional)
 * @param string $userKey User key
 * @param int $specialRequest Special request type: 0=None, 1=Birthday, 2=Anniversary (optional)
 * @param string $specialRequestNote Special request note (optional)
 * @return string|bool Order number if successful, false otherwise
 */
function book_floor($name, $phone, $useDate, $useZone, $voucher = '', $agent = '', $remark = 'Entire Floor Booking', $amount = 0, $userKey = 'TEST-KEY-001', $specialRequest = 0, $specialRequestNote = '', $paymentType = 'Transfer', $bfl = 0) {
    try {
        // Log the booking attempt
        error_log("Attempting to book entire floor: Floor {$useZone} for date {$useDate}");
        file_put_contents("log_floor_booking.txt", "Floor booking: {$useZone} for {$useDate}\n", FILE_APPEND);

        // Validate inputs
        if (empty($name) || empty($phone) || empty($useDate) || empty($useZone)) {
            $missing = [];
            if (empty($name)) $missing[] = 'name';
            if (empty($phone)) $missing[] = 'phone';
            if (empty($useDate)) $missing[] = 'useDate';
            if (empty($useZone)) $missing[] = 'useZone';

            $error_msg = "Missing required fields for floor booking: " . implode(', ', $missing);
            error_log($error_msg);
            file_put_contents("log_floor_booking_error.txt", $error_msg . "\n", FILE_APPEND);
            throw new Exception($error_msg);
        }

        // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database
        $originalDate = $useDate;
        if (preg_match('/^\d{2}-\d{2}-\d{4}$/', $useDate)) {
            // Convert from dd-mm-yyyy to yyyy-mm-dd
            $dateParts = explode('-', $useDate);
            $useDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
            error_log("Date converted from {$originalDate} to {$useDate}");
            file_put_contents("log_floor_booking.txt", "Date converted from {$originalDate} to {$useDate}\n", FILE_APPEND);
        }

        // Set cruise ID
        $cruiseId = 1;  // Hardcoded for now
        $zoneId = $useZone;

        // Get all tables for the selected floor
        $tables = [];
        $prefix = '';

        // Determine the prefix based on floor number
        if ($useZone == '1') {
            $prefix = 'C';
            $tables[] ="C1,C1-1,C1-2,C1-3,C2,C2-1,C2-2,C2-3,C2-4,C2-5,C3,C3-1,C3-2,C3-3,C3-4,C3-5,C4,C4-1,C4-2,C4-3,C4-4,C4-5,C5,C5-1,C5-2,C5-3,C5-4,C6,C6-1,C6-2,C6-3,C6-4,C7,C7-1,C7-2,C7-3,C7-4,C8,C8-1,C8-2,C8-3,C8-4,C9,C9-1,C9-2,C9-3,C9-4,C9-5,C10,C10-1,C10-2,C10-3,C10-4,C11,C11-1,C11-2,C11-3,C11-4,C11-5,C12,C12-1,C12-2,C12-3,C14,C14-1,C14-2,C14-3,C14-4,C14-5,C15,C15-1,C15-2,C15-3,C15-4,C15-5,C16,C16-1,C16-2,C16-3,C16-4,C16-5,C17,C17-1,C17-2,C17-3,C17-4,C18,C18-1,C18-2,C18-3,C18-4,C19,C19-1,C19-2,C19-3,C19-4,C20,C20-1,C20-2,C20-3,C20-4,C21,C21-1,C21-2,C21-3,C21-4,C21-5,C22,C22-1,C22-2,C22-3,C22-4,C23,C23-1,C23-2,C23-3,C23-4,C23-5";
        } else if ($useZone == '2') {
            $prefix = 'H';
            $tables[] ="H1,H1-1,H1-2,H2,H2-1,H2-2,H2-3,H3,H3-1,H3-2,H3-3,H4,H4-1,H4-2,H4-3,H5,H5-1,H5-2,H5-3,H6,H6-1,H6-2,H6-3,H7,H7-1,H7-2,H8,H8-1,H8-2,H8-3,H9,H9-1,H9-2,H9-3,H10,H10-1,H10-2,H10-3,H11,H11-1,H11-2,H11-3,H12,H12-1,H12-2,H12-3,H14,H15,H15-1,H16,H16-1,H16-2,H16-3,H16-4,H17,H17-1,H17-2,H17-3,H17-4,H17-5,B1,B1-1,B1-2,B1-3,B2,B2-1,B2-2,B2-3,B3,B3-1,B3-2,B3-3,B4,B4-1,B4-2,B4-3,B5,B5-1,B5-2,B5-3,B5-4,B6,B6-1,B6-2,B6-3,B6-4,B7,B7-1,B7-2,B7-3,B8,B8-1,B8-2,B8-3,B8-4,B9,B9-1,B9-2,B9-3,B10,B10-1,B10-2,B10-3,B10-4,B11,B11-1,B11-2,B11-3,B11-4,B12,B12-1,B12-2,B12-3,B14,B14-1,B14-2,B14-3,B15,B15-1,B15-2,B15-3,B16,B16-1,B16-2,B16-3,B17,B17-1,B17-2,B17-3,B17-4,B18,B18-1,B18-2,B18-3,B18-4,B19,B19-1,B19-2,B19-3,B20,B20-1,B20-2,B20-3,B20-4,B21,B21-1,B21-2,B21-3,B22,B22-1,B22-2,B22-3,B22-4,B23,B23-1,B23-2,B23-3,B23-4,B24,B24-1,B25,B26";
        } else if ($useZone == '3') {
            $prefix = 'A';
            $tables[] ="A1,A1-1,A1-2,A1-3,A1-4,A1-5,A2,A2-1,A2-2,A2-3,A2-4,A2-5,A2-6,A3,A3-1,A3-2,A3-3,A3-4,A3-5,A3-6,A4,A4-1,A4-2,A4-3,A4-4,A4-5,A4-6,A5,A5-1,A5-2,A5-3,A5-4,A5-5,A5-6,A6,A6-1,A6-2,A6-3,A6-4,A6-5,A6-6,A7,A7-1,A7-2,A7-3,A7-4,A7-5,A7-6,A8,A8-1,A8-2,A8-3,A8-4,A8-5,A8-6,A9,A9-1,A9-2,A9-3,A9-4,A9-5,A9-6,A10,A10-1,A10-2,A10-3,A10-4,A10-5,A11,A11-1,A11-2,A11-3,A11-4,A11-5,A12,A12-1,A12-2,A12-3,A12-4,A12-5,A14,A14-1,A14-2,A14-3,A14-4,A14-5,A15,A15-1,A15-2,A15-3,A15-4,A16,A16-1,A16-2,A16-3,A16-4,A17,A17-1,A17-2,A17-3,A18,A18-1,A18-2,A19,A19-1,A19-2,A19-3,A20,A20-1,A20-2,A20-3,A20-4,A20-5,A21,A21-1,A21-2,A21-3,A21-4,A21-5,A22,A22-1,A22-2,A22-3,A22-4,A22-5,A23,A23-1,A23-2,A23-3,A23-4,A23-5,A24,A24-1,A24-2,A24-3,A24-4,A24-5,A25,A25-1,A25-2,A25-3,A25-4,A25-5,A26,A26-1,A26-2,A26-3,A26-4,A26-5,A27,A27-1,A27-2,A27-3,A27-4,A27-5,A28,A28-1,A28-2,A28-3,A28-4,A28-5,A29,A29-1,A29-2,A29-3,A29-4,A29-5,A30,A30-1,A30-2,A30-3,A30-4,A30-5,A31,A31-1,A31-2,A31-3,A31-4,A31-5,A32,A32-1,A32-2,A32-3,A32-4,A32-5,A33,A33-1,A33-2,A33-3,A33-4,A34,A34-1,A34-2,A34-3,A35,A35-1,A35-2,A36,A36-1,A36-2";
        } else {
            $error_msg = "Invalid floor number: {$useZone}";
            error_log($error_msg);
            file_put_contents("log_floor_booking_error.txt", $error_msg . "\n", FILE_APPEND);
            throw new Exception($error_msg);
        }

        // Generate table IDs
        // for ($row = 1; $row <= 8; $row++) {
        //     for ($table = 1; $table <= 15; $table++) {
        //         $tables[] = "{$prefix}{$row}/{$table}";
        //     }
        // }

        if (empty($tables)) {
            $error_msg = "No tables found for floor {$useZone}";
            error_log($error_msg);
            file_put_contents("log_floor_booking_error.txt", $error_msg . "\n", FILE_APPEND);
            throw new Exception($error_msg);
        }

        // Convert array to comma-separated string
        $tableString = implode(',', $tables);

        // Log the table string for debugging (with slashes)
        error_log("Table string for floor {$useZone} (with slashes): " . substr($tableString, 0, 100) . "...");
        file_put_contents("log_floor_tables.txt", "Floor {$useZone} tables (with slashes): {$tableString}\n", FILE_APPEND);

        // Remove slashes from table IDs before saving to database
        $tablesWithoutSlashes = [];
        foreach ($tables as $table) {
            $tablesWithoutSlashes[] = str_replace('/', '', $table);
        }

        // Convert array to comma-separated string (without slashes)
        $tableStringWithoutSlashes = implode(',', $tablesWithoutSlashes);

        // Log the table string without slashes for debugging
        error_log("Table string for floor {$useZone} (without slashes): " . substr($tableStringWithoutSlashes, 0, 100) . "...");
        file_put_contents("log_floor_tables.txt", "Floor {$useZone} tables (without slashes): {$tableStringWithoutSlashes}\n", FILE_APPEND);

        // Log all parameters before calling insert_booking
        $debug_info = "Calling insert_booking with parameters: " . json_encode([
            'name' => $name,
            'phone' => $phone,
            'useDate' => $useDate,
            'useZone' => $useZone,
            'voucher' => $voucher,
            'agent' => $agent, 
            'remark' => $remark . ' (Entire Floor Booking)',
            'cruiseId' => $cruiseId,
            'zoneId' => $zoneId,
            'tableString' => substr($tableStringWithoutSlashes, 0, 50) . '...',
            'amount' => $amount,
            'paymentType' => $paymentType,
            'userKey' => $userKey
        ]);
        error_log($debug_info);
        file_put_contents("log_floor_booking_params.txt", $debug_info . "\n", FILE_APPEND);

        // Insert booking with all tables (without slashes)
        $orderNo = insert_booking(
            $name,
            $phone,
            0, // adult
            0, // child
            0, // infant
            0, // guide
            0, // foc
            0, // tl
            $useDate,
            $useZone,
            $voucher,
            $agent,
            $remark . ' (Entire Floor Booking)',
            $cruiseId,
            $zoneId,
            $tableStringWithoutSlashes, // Use the version without slashes
            $amount,
            $paymentType, // payment type from floor booking form
            $userKey,
            'WP',
            $specialRequest, // special request from floor booking form
            $specialRequestNote // special request note from floor booking form
        );

        if (!$orderNo) {
            $error_msg = "Failed to get order number from insert_booking";
            error_log($error_msg);
            file_put_contents("log_floor_booking_error.txt", $error_msg . "\n", FILE_APPEND);
            throw new Exception($error_msg);
        }

        error_log("Floor booking successful: Order #{$orderNo}");
        file_put_contents("log_floor_booking_success.txt", "Floor {$useZone} booked successfully: Order #{$orderNo}\n", FILE_APPEND);
        return $orderNo;
    } catch (Exception $e) {
        error_log("Error booking entire floor: " . $e->getMessage());
        file_put_contents("log_floor_booking_error.txt", "Error: " . $e->getMessage() . "\n", FILE_APPEND);
        return false;
    }
}

// function edit Booking
function edit_booking($id, $useDate, $useZone, $name, $phone, $adult, $child, $infant, $guide, $foc, $tl, $voucher, $agent, $amount, $remark, $floor = '', $table = '', $specialRequest = 0) {
    // set timezone to Asia/Bangkok
    date_default_timezone_set('Asia/Bangkok');
    // convert date to Y-m-d format
    $useDate = date('Y-m-d', strtotime($useDate));
    // convert zone to int
    $useZone = intval($useZone);
    // convert adult to int
    $adult = intval($adult);
    // convert child to int
    $child = intval($child);
    // convert infant to int
    $infant = intval($infant);
    // convert guide to int
    $guide = intval($guide);
    // convert foc to int
    $foc = intval($foc);
    // convert tl to int
    $tl = intval($tl);
    // convert amount to float
    $amount = floatval($amount);
    // convert id to int
    $id = intval($id);

    try {
        // include dbconnect
        $conn = db_connect();

        // Use prepared statement to prevent SQL injection
        $sql = "UPDATE kp_booking SET
                use_date = :useDate,
                zone_id = :useZone,
                name = :name,
                phone = :phone,
                adult = :adult,
                child = :child,
                infant = :infant,
                guide = :guide,
                inspection = :foc,
                team_leader = :tl,
                voucher = :voucher,
                agent = :agent,
                amount = :amount,
                remark = :remark,
                use_zone = :floor,
                tables = :table,
                special_request = :specialRequest
                WHERE booking_id = :id";

        $stmt = $conn->prepare($sql);

        // Bind parameters with appropriate types
        $stmt->bindParam(':useDate', $useDate, PDO::PARAM_STR);
        $stmt->bindParam(':useZone', $useZone, PDO::PARAM_INT);
        $stmt->bindParam(':name', $name, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':adult', $adult, PDO::PARAM_INT);
        $stmt->bindParam(':child', $child, PDO::PARAM_INT);
        $stmt->bindParam(':infant', $infant, PDO::PARAM_INT);
        $stmt->bindParam(':guide', $guide, PDO::PARAM_INT);
        $stmt->bindParam(':foc', $foc, PDO::PARAM_INT);
        $stmt->bindParam(':tl', $tl, PDO::PARAM_INT);
        $stmt->bindParam(':voucher', $voucher, PDO::PARAM_STR);
        $stmt->bindParam(':agent', $agent, PDO::PARAM_STR);
        $stmt->bindParam(':amount', $amount, PDO::PARAM_STR); // Use STR for float values
        $stmt->bindParam(':remark', $remark, PDO::PARAM_STR);
        $stmt->bindParam(':floor', $floor, PDO::PARAM_STR);
        $stmt->bindParam(':table', $table, PDO::PARAM_STR);
        $stmt->bindParam(':specialRequest', $specialRequest, PDO::PARAM_INT);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);

        $stmt->execute();

        // create json file by date
        create_json_file_by_date($useDate);

        // create json file by zone
        create_json_file_by_zone($useDate);

        return true;
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        error_log("Database error while updating booking: " . $e->getMessage());
        throw new Exception("Failed to update booking. Please try again later.");
    }
}

function delete_booking($id) {
    // Convert id to integer for safety
    $id = intval($id);

    try {
        // include dbconnect
        $conn = db_connect();

        // First get the booking date to update JSON files later
        $sql_get = "SELECT use_date FROM kp_booking WHERE booking_id = :id";
        $stmt_get = $conn->prepare($sql_get);
        $stmt_get->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt_get->execute();

        $useDate = null;
        if ($row = $stmt_get->fetch()) {
            $useDate = $row['use_date'];
        }

        // Use prepared statement to prevent SQL injection
        $sql = "DELETE FROM kp_booking WHERE booking_id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        // Update JSON files if we have the date
        if ($useDate) {
            create_json_file_by_date($useDate);
            create_json_file_by_zone($useDate);
        }

        return true;
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        error_log("Database error while deleting booking: " . $e->getMessage());
        throw new Exception("Failed to delete booking. Please try again later.");
    }
}


function get_browser_bookings($useDate, $useZone='') {
    try {
        // include dbconnect
        $conn = db_connect();

        // Use prepared statement to prevent SQL injection
        $sql = "SELECT * FROM kp_booking WHERE use_date = :useDate AND zone_id = :useZone";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':useDate', $useDate, PDO::PARAM_STR);
        $stmt->bindParam(':useZone', $useZone, PDO::PARAM_STR);
        $stmt->execute();

        return $stmt;
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        error_log("Database error while getting bookings: " . $e->getMessage());
        throw new Exception("Failed to retrieve bookings. Please try again later.");
    }
}









?>