<?php
/**
 * Session Class
 *
 * Handles session management with security features
 */
class Session {
    /**
     * Start a secure session
     */
    public static function start() {
        // Include config file if not already included
        if (!defined('SESSION_NAME')) {
            require_once dirname(__DIR__) . '/config/config.php';
        }

        // Set session cookie parameters
        session_name(SESSION_NAME);

        // Set session cookie parameters
        session_set_cookie_params([
            'lifetime' => SESSION_LIFETIME,
            'path' => '/',
            'domain' => '',
            'secure' => SESSION_SECURE,
            'httponly' => SESSION_HTTP_ONLY,
            'samesite' => 'Lax'
        ]);

        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Check if session has expired
        self::checkSessionExpiry();

        // Regenerate session ID periodically to prevent session fixation
        self::regenerateSessionId();

        // Set last activity time
        $_SESSION['last_activity'] = time();
    }

    /**
     * Set a session variable
     *
     * @param string $key Session key
     * @param mixed $value Session value
     */
    public static function set($key, $value) {
        $_SESSION[$key] = $value;
    }

    /**
     * Get a session variable
     *
     * @param string $key Session key
     * @param mixed $default Default value if key doesn't exist
     * @return mixed Session value or default
     */
    public static function get($key, $default = null) {
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }

    /**
     * Check if a session variable exists
     *
     * @param string $key Session key
     * @return bool True if exists, false otherwise
     */
    public static function has($key) {
        return isset($_SESSION[$key]);
    }

    /**
     * Remove a session variable
     *
     * @param string $key Session key
     */
    public static function remove($key) {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }

    /**
     * Destroy the session
     */
    public static function destroy() {
        // Unset all session variables
        $_SESSION = [];

        // Delete the session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }

        // Destroy the session
        session_destroy();
    }

    /**
     * Check if user is logged in
     *
     * @return bool True if logged in, false otherwise
     */
    public static function isLoggedIn() {
        // For API requests, also check for API token in header
        if (self::isApiRequest()) {
            return self::validateApiRequest();
        }

        return self::has('loggedin') && self::get('loggedin') === true;
    }

    /**
     * Check if current request is an API request
     *
     * @return bool True if API request, false otherwise
     */
    private static function isApiRequest() {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        return (strpos($requestUri, '/api/') !== false);
    }

    /**
     * Validate API request
     *
     * @return bool True if valid, false otherwise
     */
    private static function validateApiRequest() {
        // First check if session is already authenticated
        if (self::has('loggedin') && self::get('loggedin') === true) {
            return true;
        }

        // For development/testing, allow all API requests
        // TODO: Implement proper API authentication in production
        return true;
    }

    /**
     * Check if session has expired
     */
    private static function checkSessionExpiry() {
        if (self::has('last_activity')) {
            $inactiveTime = time() - self::get('last_activity');

            // If inactive for too long, destroy session
            if ($inactiveTime > SESSION_LIFETIME) {
                self::destroy();

                // Redirect to login page
                header('Location: /login/index.php?session_expired=1');
                exit;
            }
        }
    }

    /**
     * Regenerate session ID periodically
     */
    private static function regenerateSessionId() {
        // Regenerate session ID every 30 minutes
        if (!self::has('session_regenerated') || time() - self::get('session_regenerated') > 1800) {
            session_regenerate_id(true);
            self::set('session_regenerated', time());
        }
    }

    /**
     * Generate and store CSRF token
     *
     * @return string CSRF token
     */
    public static function generateCsrfToken() {
        $token = bin2hex(random_bytes(32));
        self::set(CSRF_TOKEN_NAME, $token);
        return $token;
    }

    /**
     * Validate CSRF token
     *
     * @param string $token Token to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateCsrfToken($token) {
        if (!self::has(CSRF_TOKEN_NAME) || empty($token)) {
            return false;
        }

        return hash_equals(self::get(CSRF_TOKEN_NAME), $token);
    }
}
