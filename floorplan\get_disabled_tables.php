<?php
// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Connect to database
    $conn = db_connect();
    
    // Get current date
    $currentDate = date('Y-m-d');
    
    // Query to get disabled tables
    // This gets tables that are either:
    // 1. Disabled with no end date (indefinitely)
    // 2. Disabled with an end date in the future
    $sql = "SELECT table_id, disabled_until FROM kp_tables 
            WHERE status = 'disabled' 
            AND (disabled_until IS NULL OR disabled_until >= :currentDate)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':currentDate', $currentDate);
    $stmt->execute();
    
    // Fetch results
    $disabledTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $disabledTables[] = [
            'id' => $row['table_id'],
            'disabled_until' => $row['disabled_until']
        ];
    }
    
    // Return disabled tables as JSON
    echo json_encode($disabledTables);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
