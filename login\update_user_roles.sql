-- Update the kp_login table to support new role structure
-- First, modify the role enum to include super_admin, admin, user

-- Drop the existing role constraint and add the new one
ALTER TABLE `kp_login` 
MODIFY COLUMN `role` ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user';

-- Insert default users if they don't exist

-- Super Admin user
INSERT IGNORE INTO `kp_login` (
    `user`, 
    `pass`, 
    `name`, 
    `email`, 
    `role`, 
    `status`, 
    `user_key`, 
    `create_date`
) VALUES (
    'superadmin', 
    '$argon2id$v=19$m=65536,t=4,p=3$YourSaltHere$YourHashHere', -- Will be updated by <PERSON><PERSON> script
    'Super Administrator', 
    '<EMAIL>', 
    'super_admin', 
    1, 
    'superadmin_key_123456', 
    NOW()
);

-- Admin user
INSERT IGNORE INTO `kp_login` (
    `user`, 
    `pass`, 
    `name`, 
    `email`, 
    `role`, 
    `status`, 
    `user_key`, 
    `create_date`
) VALUES (
    'admin', 
    '$argon2id$v=19$m=65536,t=4,p=3$YourSaltHere$YourHashHere', -- Will be updated by PHP script
    'Administrator', 
    '<EMAIL>', 
    'admin', 
    1, 
    'admin_key_123456', 
    NOW()
);

-- Regular user
INSERT IGNORE INTO `kp_login` (
    `user`, 
    `pass`, 
    `name`, 
    `email`, 
    `role`, 
    `status`, 
    `user_key`, 
    `create_date`
) VALUES (
    'user', 
    '$argon2id$v=19$m=65536,t=4,p=3$YourSaltHere$YourHashHere', -- Will be updated by PHP script
    'Regular User', 
    '<EMAIL>', 
    'user', 
    1, 
    'user_key_123456', 
    NOW()
);
