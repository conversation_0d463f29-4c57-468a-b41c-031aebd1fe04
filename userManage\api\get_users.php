<?php
session_start();
require_once '../../includes/role_check.php';
require_once '../../dbconnect/_dbconnect.php';
// Check if user is logged in and has admin access
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();

    // Get all users from database
    $stmt = $pdo->prepare("SELECT id, user as username, email, name, role, status, created_at FROM kp_login ORDER BY created_at DESC");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the data
    foreach ($users as &$user) {
        // Ensure status is integer
        $user['status'] = (int)$user['status'];
        
        // Format created_at if it exists
        if ($user['created_at']) {
            $user['created_at'] = date('Y-m-d H:i:s', strtotime($user['created_at']));
        }
    }
    
    echo json_encode([
        'success' => true,
        'users' => $users,
        'count' => count($users)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
