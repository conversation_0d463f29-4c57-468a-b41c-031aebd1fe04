<?php
/**
 * Save Settings Handler
 * 
 * Processes settings update requests
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Check if user has admin role
if (Session::get('role') !== 'admin') {
    header('Location: ../index.php?error=unauthorized');
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit;
}

// Validate CSRF token
$token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
if (!Session::validateCsrfToken($token)) {
    header('Location: index.php?error=invalid_token');
    exit;
}

// Validate and sanitize inputs
$companyName = isset($_POST['company_name']) ? Security::sanitizeInput($_POST['company_name']) : '';
$companyAddress = isset($_POST['company_address']) ? Security::sanitizeInput($_POST['company_address']) : '';
$companyPhone = isset($_POST['company_phone']) ? Security::sanitizeInput($_POST['company_phone']) : '';
$companyEmail = isset($_POST['company_email']) ? Security::sanitizeInput($_POST['company_email']) : '';
$bookingStartTime = isset($_POST['booking_start_time']) ? Security::sanitizeInput($_POST['booking_start_time']) : '';
$bookingEndTime = isset($_POST['booking_end_time']) ? Security::sanitizeInput($_POST['booking_end_time']) : '';
$bookingInterval = isset($_POST['booking_interval']) ? (int)$_POST['booking_interval'] : 30;
$maxDaysAdvance = isset($_POST['max_days_advance']) ? (int)$_POST['max_days_advance'] : 30;
$defaultCruiseId = isset($_POST['default_cruise_id']) ? (int)$_POST['default_cruise_id'] : 1;
$enableNotifications = isset($_POST['enable_notifications']) ? true : false;
$maintenanceMode = isset($_POST['maintenance_mode']) ? true : false;

// Validate required fields
if (empty($companyName)) {
    header('Location: index.php?error=missing_fields');
    exit;
}

// Validate email if provided
if (!empty($companyEmail) && !Security::validateEmail($companyEmail)) {
    header('Location: index.php?error=invalid_email');
    exit;
}

// Validate booking interval
if (!in_array($bookingInterval, [15, 30, 60])) {
    $bookingInterval = 30; // Default to 30 minutes
}

// Validate max days advance
if ($maxDaysAdvance < 1 || $maxDaysAdvance > 365) {
    $maxDaysAdvance = 30; // Default to 30 days
}

// Validate default cruise ID
if ($defaultCruiseId < 1) {
    $defaultCruiseId = 1; // Default to 1
}

// Prepare settings data
$settings = [
    'company_name' => $companyName,
    'company_address' => $companyAddress,
    'company_phone' => $companyPhone,
    'company_email' => $companyEmail,
    'booking_start_time' => $bookingStartTime,
    'booking_end_time' => $bookingEndTime,
    'booking_interval' => $bookingInterval,
    'max_days_advance' => $maxDaysAdvance,
    'default_cruise_id' => $defaultCruiseId,
    'enable_notifications' => $enableNotifications,
    'maintenance_mode' => $maintenanceMode,
    'last_updated' => date('Y-m-d H:i:s'),
    'updated_by' => Session::get('username')
];

// Save settings to file
$settingsFile = dirname(__DIR__) . '/config/settings.json';
$result = file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));

if ($result !== false) {
    // Log the action
    error_log("User {$_SESSION['username']} updated system settings");
    
    // Redirect to success page
    header('Location: index.php?success=1');
} else {
    // Redirect to error page
    header('Location: index.php?error=write_error');
}
exit;
