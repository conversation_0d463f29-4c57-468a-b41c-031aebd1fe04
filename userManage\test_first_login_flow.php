<?php
// Test script for first-time login flow
require_once '../dbconnect/_dbconnect.php';

echo "<h2>Testing First-Time Login Flow</h2>";

try {
    $pdo = db_connect();
    
    // Check if first_login column exists
    echo "<h3>1. Database Schema Check</h3>";
    $checkSql = "DESCRIBE kp_login";
    $stmt = $pdo->query($checkSql);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasFirstLogin = false;
    while ($row = $stmt->fetch()) {
        if ($row['Field'] === 'first_login') {
            $hasFirstLogin = true;
            echo "<tr style='background-color: #d4edda;'>";
        } else {
            echo "<tr>";
        }
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($hasFirstLogin) {
        echo "<p style='color: green;'>✅ first_login column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ first_login column missing</p>";
    }
    
    // Check existing users
    echo "<h3>2. Current Users Status</h3>";
    $usersSql = "SELECT id, user, role, status, first_login FROM kp_login ORDER BY id";
    $stmt = $pdo->query($usersSql);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Status</th><th>First Login</th><th>Action Required</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['user']) . "</td>";
        echo "<td>" . htmlspecialchars($row['role']) . "</td>";
        echo "<td>" . ($row['status'] ? 'Active' : 'Inactive') . "</td>";
        echo "<td>" . ($row['first_login'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($row['first_login'] ? 'Must change password' : 'Normal login') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test password hashing
    echo "<h3>3. Password Hashing Test</h3>";
    $testPassword = 'pwd123#@!';
    $hashedPassword = password_hash($testPassword, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3,
    ]);
    
    echo "<p><strong>Test Password:</strong> <code>" . htmlspecialchars($testPassword) . "</code></p>";
    echo "<p><strong>Hashed Password:</strong> <code>" . htmlspecialchars(substr($hashedPassword, 0, 50)) . "...</code></p>";
    
    $verifyResult = password_verify($testPassword, $hashedPassword);
    echo "<p><strong>Verification Test:</strong> " . ($verifyResult ? '✅ Pass' : '❌ Fail') . "</p>";
    
    // Test API endpoints
    echo "<h3>4. API Endpoints Test</h3>";
    
    $apiFiles = [
        'api/add_user.php' => 'Add User API',
        'api/get_users.php' => 'Get Users API',
        'api/get_user.php' => 'Get Single User API',
        'api/update_user.php' => 'Update User API',
        'api/delete_user.php' => 'Delete User API'
    ];
    
    foreach ($apiFiles as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ <strong>$description:</strong> File exists</p>";
        } else {
            echo "<p>❌ <strong>$description:</strong> File missing</p>";
        }
    }
    
    // Test login pages
    echo "<h3>5. Login Pages Test</h3>";
    
    $loginFiles = [
        '../login/index.php' => 'Login Page',
        '../login/login_process.php' => 'Login Process',
        '../login/change_password.php' => 'Change Password Page'
    ];
    
    foreach ($loginFiles as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ <strong>$description:</strong> File exists</p>";
        } else {
            echo "<p>❌ <strong>$description:</strong> File missing</p>";
        }
    }
    
    echo "<h3>6. Test Summary</h3>";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Implementation Status:</h4>";
    echo "<ul>";
    echo "<li>✅ Database schema updated with first_login field</li>";
    echo "<li>✅ Add user modal updated (password field removed)</li>";
    echo "<li>✅ Add user API updated (sets default password and first_login=1)</li>";
    echo "<li>✅ Change password page created</li>";
    echo "<li>✅ Login process updated (checks first_login flag)</li>";
    echo "<li>✅ User management JavaScript updated (shows default password info)</li>";
    echo "</ul>";
    
    echo "<h4>Testing Steps:</h4>";
    echo "<ol>";
    echo "<li>Go to User Management page</li>";
    echo "<li>Add a new user (password field should be gone)</li>";
    echo "<li>Try to login with the new user using default password 'pwd123#@!'</li>";
    echo "<li>Should be redirected to change password page</li>";
    echo "<li>Change password and verify redirect to dashboard</li>";
    echo "<li>Logout and login again with new password (should go directly to dashboard)</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { width: 100%; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
