<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Validate input
    if (!isset($_POST['id']) || !isset($_POST['status'])) {
        throw new Exception('Missing required parameters');
    }
    
    $tableId = $_POST['id'];
    $status = $_POST['status'];
    
    // Validate status
    if ($status !== 'active' && $status !== 'disabled') {
        throw new Exception('Invalid status value');
    }
    
    // Connect to database
    $conn = db_connect();
    
    // Check if table exists
    $checkSql = "SELECT COUNT(*) FROM kp_tables WHERE table_id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':id', $tableId);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() == 0) {
        throw new Exception('Table not found');
    }
    
    // Update table status
    $sql = "UPDATE kp_tables SET status = :status WHERE table_id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':id', $tableId);
    $stmt->execute();
    
    // Return success
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
