  <script src="../assets/js/vendor.min.js"></script>
  <!-- Import Js Files -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
  <script src="../assets/js/theme/sidebarmenu.js"></script>

  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
  <script src="../assets/libs/fullcalendar/index.global.min.js"></script>
  <script src="../assets/js/apps/contact.js"></script>
  <script src="../assets/libs/prismjs/prism.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->

  <!-- Bootstrap Datepicker -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

  <script>
    $(document).ready(function() {
        // Initialize datepicker
        initializeDatepicker();

        // Load table data on page load
        loadTableData();

        // Filter table data when filters change
        $('#table-row-filter, #table-status-filter').on('change', function() {
            loadTableData();
        });

        // Date change handler
        $('#myDate').on('change', function() {
            const selectedDate = $(this).val();
            $('#selected-date-display').text(selectedDate);

            showLoading();
            setTimeout(function() {
                loadTableData();
                hideLoading();
            }, 500);
        });

        // Preview tables
        $('#preview-tables').on('click', function(e) {
            e.preventDefault();
            previewTables();
        });

        // Submit form to add tables
        $('#add-table-form').on('submit', function(e) {
            e.preventDefault();
            addTables();
        });

        // Function to load table data
        function loadTableData() {
            const rowFilter = $('#table-row-filter').val();
            const statusFilter = $('#table-status-filter').val();
            const selectedDate = $('#myDate').val();

            // Show loading state
            const tableBody = $('#table-list tbody');
            tableBody.html(`
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        Loading table data...
                    </td>
                </tr>
            `);

            $.ajax({
                url: 'get_tables.php',
                type: 'GET',
                data: {
                    row: rowFilter,
                    status: statusFilter,
                    selected_date: selectedDate
                },
                dataType: 'json',
                success: function(data) {
                    renderTableData(data);
                    updateTableSummary(data);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading table data:', error);
                    tableBody.html(`
                        <tr>
                            <td colspan="5" class="text-center py-4 text-danger">
                                <i class="ti ti-alert-circle me-2"></i>
                                Failed to load table data. Please try again.
                            </td>
                        </tr>
                    `);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to load table data. Please try again.'
                    });
                }
            });
        }

        // Function to render table data
        function renderTableData(data) {
            const tableBody = $('#table-list tbody');
            tableBody.empty();

            if (data.length === 0) {
                tableBody.append('<tr><td colspan="5" class="text-center">No tables found</td></tr>');
                return;
            }

            data.forEach(function(table) {
                const row = $('<tr></tr>');

                // Get row identifier for color coding
                let rowIdentifier = '';
                if (table.row) {
                    // Use the row field directly (e.g., "1A", "1B", etc.)
                    rowIdentifier = table.row;
                } else if (table.id) {
                    // Fallback: extract from table ID
                    if (table.id.includes('/')) {
                        rowIdentifier = table.id.split('/')[0]; // A1, A2, etc.
                    } else {
                        // For formats like 1A01, 1B02, etc.
                        rowIdentifier = table.id.substring(0, 2); // 1A, 1B, etc.
                    }
                }

                row.append(`<td>
                    <div class="d-flex align-items-center">
                        <span class="row-color row-${rowIdentifier}" title="Row ${rowIdentifier}"></span>
                        <strong>${table.id}</strong>
                    </div>
                </td>`);

                row.append(`<td>
                    <span class="badge bg-light text-dark">${table.row}</span>
                </td>`);

                let statusHtml = '';
                if (table.status === 'active') {
                    statusHtml = '<span class="badge bg-success"><i class="ti ti-check me-1"></i>Active</span>';
                } else {
                    statusHtml = '<span class="badge bg-danger"><i class="ti ti-x me-1"></i>Disabled</span>';
                }
                row.append(`<td>${statusHtml}</td>`);

                // Date Info column
                let dateInfoHtml = '';
                if (table.status === 'disabled' && table.disabled_until) {
                    const disabledDate = new Date(table.disabled_until);
                    const formattedDate = disabledDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                    dateInfoHtml = `
                        <div class="text-center">
                            <small class="text-muted d-block">Disabled Until</small>
                            <span class="badge bg-warning text-dark">
                                <i class="ti ti-calendar me-1"></i>${formattedDate}
                            </span>
                        </div>
                    `;
                } else if (table.status === 'disabled') {
                    dateInfoHtml = `
                        <div class="text-center">
                            <span class="badge bg-secondary">
                                <i class="ti ti-infinity me-1"></i>Indefinitely
                            </span>
                        </div>
                    `;
                } else {
                    dateInfoHtml = `
                        <div class="text-center">
                            <span class="text-muted">
                                <i class="ti ti-check me-1"></i>Available
                            </span>
                        </div>
                    `;
                }
                row.append(`<td>${dateInfoHtml}</td>`);

                const toggleBtn = table.status === 'active'
                    ? `<button class="btn btn-sm btn-outline-danger toggle-status" data-id="${table.id}" data-status="active" title="Disable Table">
                         <i class="ti ti-ban me-1"></i>Disable
                       </button>`
                    : `<button class="btn btn-sm btn-outline-success toggle-status" data-id="${table.id}" data-status="disabled" title="Enable Table">
                         <i class="ti ti-check me-1"></i>Enable
                       </button>`;

                row.append(`<td>
                    <div class="btn-group" role="group">
                        ${toggleBtn}
                        <button class="btn btn-sm btn-outline-secondary delete-table" data-id="${table.id}" title="Delete Table">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                </td>`);

                tableBody.append(row);
            });

            // Add event listeners for toggle and delete buttons
            $('.toggle-status').on('click', function() {
                const tableId = $(this).data('id');
                const currentStatus = $(this).data('status');
                const newStatus = currentStatus === 'active' ? 'disabled' : 'active';

                if (newStatus === 'disabled') {
                    // Calculate tomorrow's date for the default value
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const tomorrowFormatted = tomorrow.toISOString().split('T')[0];

                    // Show enhanced date picker popup for disabling
                    Swal.fire({
                        title: '<i class="ti ti-ban me-2"></i>Disable Table',
                        html: `
                            <div class="form-group text-start">
                                <label for="disabled-until-date" class="form-label fw-bold">
                                    <i class="ti ti-calendar me-1"></i>Disable Until Date
                                </label>
                                <input type="date" id="disabled-until-date" class="form-control"
                                       min="${tomorrowFormatted}" value="${tomorrowFormatted}">
                                <div class="form-check mt-3">
                                    <input class="form-check-input" type="checkbox" id="disable-indefinitely">
                                    <label class="form-check-label" for="disable-indefinitely">
                                        <i class="ti ti-infinity me-1"></i>Disable indefinitely
                                    </label>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="ti ti-info-circle me-1"></i>
                                    Table will be unavailable for booking during the disabled period
                                </small>
                            </div>
                        `,
                        showCancelButton: true,
                        confirmButtonText: '<i class="ti ti-ban me-1"></i>Disable Table',
                        cancelButtonText: '<i class="ti ti-x me-1"></i>Cancel',
                        confirmButtonColor: '#dc3545',
                        didOpen: () => {
                            // Handle indefinite disable checkbox
                            const checkbox = document.getElementById('disable-indefinitely');
                            const dateInput = document.getElementById('disabled-until-date');

                            checkbox.addEventListener('change', function() {
                                dateInput.disabled = this.checked;
                                if (this.checked) {
                                    dateInput.value = '';
                                } else {
                                    dateInput.value = tomorrowFormatted;
                                }
                            });
                        },
                        preConfirm: () => {
                            const selectedDate = document.getElementById('disabled-until-date').value;
                            const indefinite = document.getElementById('disable-indefinitely').checked;

                            if (!indefinite && selectedDate) {
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);

                                const selected = new Date(selectedDate);
                                selected.setHours(0, 0, 0, 0);

                                if (selected <= today) {
                                    Swal.showValidationMessage('Please select a future date');
                                    return false;
                                }
                                return selectedDate;
                            }
                            return indefinite ? '' : selectedDate;
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Show loading state
                            Swal.fire({
                                title: 'Disabling Table...',
                                html: '<div class="spinner-border text-primary" role="status"></div>',
                                showConfirmButton: false,
                                allowOutsideClick: false
                            });

                            toggleTableStatus(tableId, newStatus, result.value);
                        }
                    });
                } else {
                    // Enable table immediately
                    toggleTableStatus(tableId, newStatus);
                }
            });

            $('.delete-table').on('click', function() {
                const tableId = $(this).data('id');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        deleteTable(tableId);
                    }
                });
            });
        }

        // Function to toggle table status
        function toggleTableStatus(tableId, newStatus, disabledUntil = null) {
            const data = {
                id: tableId,
                status: newStatus
            };

            // Add disabled_until date if provided
            if (disabledUntil) {
                data.disabled_until = disabledUntil;
            }

            $.ajax({
                url: 'update_table_status.php',
                type: 'POST',
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let successMessage = 'Table status updated successfully!';
                        let successTitle = 'Success';

                        if (newStatus === 'disabled') {
                            successTitle = 'Table Disabled';
                            if (disabledUntil) {
                                // Format the date to be more user-friendly
                                const disabledDate = new Date(disabledUntil);
                                const formattedDate = disabledDate.toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                });
                                successMessage = `Table will be automatically re-enabled on ${formattedDate}`;
                            } else {
                                successMessage = 'Table has been disabled indefinitely';
                            }
                        } else {
                            successTitle = 'Table Enabled';
                            successMessage = 'Table has been successfully enabled';
                        }

                        Swal.fire({
                            icon: 'success',
                            title: successTitle,
                            text: successMessage,
                            timer: 2000
                        });
                        loadTableData();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Failed to update table status: ' + response.message
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error updating table status:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to update table status. Please try again.'
                    });
                }
            });
        }

        // Function to delete table
        function deleteTable(tableId) {
            $.ajax({
                url: 'delete_table.php',
                type: 'POST',
                data: {
                    id: tableId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Deleted!',
                            text: 'Table has been deleted.',
                            timer: 1500
                        });
                        loadTableData();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Failed to delete table: ' + response.message
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting table:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to delete table. Please try again.'
                    });
                }
            });
        }

        // Function to preview tables
        function previewTables() {
            const row = $('#table-row').val();
            const start = parseInt($('#table-start').val());
            const end = parseInt($('#table-end').val());
            const xStart = parseInt($('#table-x-start').val());
            const yPos = parseInt($('#table-y-position').val());
            const width = parseInt($('#table-width').val());
            const height = parseInt($('#table-height').val());
            const spacing = parseInt($('#table-spacing').val());

            // Validate inputs
            if (!row || isNaN(start) || isNaN(end) || isNaN(xStart) || isNaN(yPos) ||
                isNaN(width) || isNaN(height) || isNaN(spacing)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Please fill in all fields with valid numbers'
                });
                return;
            }

            if (start > end) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Start number must be less than or equal to end number'
                });
                return;
            }

            // Clear previous preview
            const svg = $('#preview-svg');
            svg.empty();

            // Get color for the row
            let fillColor = '#539bff';
            switch(row) {
                case 'A1': fillColor = '#28a745'; break;
                case 'A2': fillColor = '#ffc107'; break;
                case 'A3': fillColor = '#dc3545'; break;
                case 'A4': fillColor = '#17a2b8'; break;
                case 'A5': fillColor = '#6f42c1'; break;
                case 'A6': fillColor = '#fd7e14'; break;
                case 'A7': fillColor = '#20c997'; break;
                case 'A8': fillColor = '#e83e8c'; break;
            }

            // Generate preview
            for (let i = start; i <= end; i++) {
                const tableId = `${row}/${i}`;
                const xPos = xStart + (i - start) * spacing;

                // Create table rectangle
                const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                rect.setAttribute('x', xPos);
                rect.setAttribute('y', yPos);
                rect.setAttribute('width', width);
                rect.setAttribute('height', height);
                rect.setAttribute('fill', fillColor);
                rect.setAttribute('stroke', '#ffffff');
                rect.setAttribute('stroke-width', '2');
                rect.setAttribute('rx', '5');
                rect.setAttribute('ry', '5');

                // Create table text
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', xPos + width / 2);
                text.setAttribute('y', yPos + height / 2 + 5);
                text.setAttribute('font-size', '17');
                text.setAttribute('fill', '#ffffff');
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('font-weight', 'bold');
                text.textContent = tableId;

                // Add to SVG
                svg.append(rect);
                svg.append(text);
            }

            // Show preview
            $('#table-preview').show();
        }

        // Function to add tables
        function addTables() {
            const row = $('#table-row').val();
            const start = parseInt($('#table-start').val());
            const end = parseInt($('#table-end').val());
            const xStart = parseInt($('#table-x-start').val());
            const yPos = parseInt($('#table-y-position').val());
            const width = parseInt($('#table-width').val());
            const height = parseInt($('#table-height').val());
            const spacing = parseInt($('#table-spacing').val());
            const status = $('#table-status').val();

            // Validate inputs
            if (!row || isNaN(start) || isNaN(end) || isNaN(xStart) || isNaN(yPos) ||
                isNaN(width) || isNaN(height) || isNaN(spacing)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Please fill in all fields with valid numbers'
                });
                return;
            }

            if (start > end) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning',
                    text: 'Start number must be less than or equal to end number'
                });
                return;
            }

            // Prepare data for submission
            const tables = [];
            for (let i = start; i <= end; i++) {
                const tableId = `${row}/${i}`;
                const xPos = xStart + (i - start) * spacing;

                tables.push({
                    id: tableId,
                    row: row,
                    x: xPos,
                    y: yPos,
                    width: width,
                    height: height,
                    status: status
                });
            }

            // Submit data
            $.ajax({
                url: 'add_tables.php',
                type: 'POST',
                data: {
                    tables: JSON.stringify(tables)
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Tables added successfully!'
                        });
                        $('#add-table-form')[0].reset();
                        $('#table-preview').hide();
                        loadTableData();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Failed to add tables: ' + response.message
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error adding tables:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to add tables. Please try again.'
                    });
                }
            });
        }

        // Initialize datepicker
        function initializeDatepicker() {
            $('#myDate').datepicker({
                format: 'dd-mm-yyyy',
                autoclose: true,
                todayHighlight: true,
                orientation: 'bottom auto'
            });
        }

        // Show loading overlay
        function showLoading() {
            $('#loading-overlay').show();
        }

        // Hide loading overlay
        function hideLoading() {
            $('#loading-overlay').hide();
        }

        // Update table summary
        function updateTableSummary(data) {
            const totalTables = data.length;
            const activeTables = data.filter(table => table.status === 'active').length;
            const disabledTables = data.filter(table => table.status === 'disabled').length;

            // Update the date status indicator with summary
            const selectedDate = $('#myDate').val();
            $('#date-status-indicator').html(`
                <i class="ti ti-calendar me-1"></i>
                <span>${selectedDate}</span>
                <span class="ms-2 badge bg-light text-dark">${totalTables} tables</span>
            `);

            // You can add more summary display here if needed
            console.log(`Summary for ${selectedDate}: ${totalTables} total, ${activeTables} active, ${disabledTables} disabled`);
        }
    });
  </script>
