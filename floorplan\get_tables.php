<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Get parameters with defaults
    $row = isset($_GET['row']) ? $_GET['row'] : 'all';
    $status = isset($_GET['status']) ? $_GET['status'] : 'all';
    
    // Connect to database
    $conn = db_connect();
    
    // Build query based on filters
    $sql = "SELECT * FROM kp_tables WHERE 1=1";
    $params = [];
    
    if ($row !== 'all') {
        $sql .= " AND table_id LIKE :row";
        $params[':row'] = $row . '%';
    } else {
        // Only get Floor 1 tables
        $sql .= " AND table_id LIKE '1%'";
    }
    
    if ($status !== 'all') {
        $sql .= " AND status = :status";
        $params[':status'] = $status;
    }
    
    $sql .= " ORDER BY table_id ASC";
    
    // Prepare and execute query
    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    
    // Fetch results
    $tables = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $tables[] = [
            'id' => $row['table_id'],
            'row' => substr($row['table_id'], 0, 2),
            'status' => $row['status'],
            'x' => $row['x_position'],
            'y' => $row['y_position'],
            'width' => $row['width'],
            'height' => $row['height']
        ];
    }
    
    // Return results
    echo json_encode($tables);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
