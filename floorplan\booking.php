<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

// Log POST data for debugging
file_put_contents("log_debug.txt", json_encode($_POST) . PHP_EOL, FILE_APPEND);

// Check if user is logged in
if (!isset($_SESSION['userKey'])) {
    // For development/testing only
    $_SESSION['userKey'] = 'TEST-KEY-001';
}

include '../dbconnect/_dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        if (empty($_POST['customer']) || empty($_POST['phone']) || empty($_POST['useDate']) || empty($_POST['tables'])) {
            throw new Exception('Missing required fields');
        }

        // Process form data
        $customername = $_POST['customer'];
        $phone = $_POST['phone'];

        // Process numeric fields
        $adult = isset($_POST['adult']) ? (int)$_POST['adult'] : 0;
        $child = isset($_POST['child']) ? (int)$_POST['child'] : 0;
        $infant = isset($_POST['infant']) ? (int)$_POST['infant'] : 0;
        $guide = isset($_POST['guide']) ? (int)$_POST['guide'] : 0;
        $foc = isset($_POST['foc']) ? (int)$_POST['foc'] : 0;
        $tl = isset($_POST['tl']) ? (int)$_POST['tl'] : 0;
        $amount = isset($_POST['amount']) ? (int)$_POST['amount'] : 0;
        $paymentType = isset($_POST['paymentType']) ? $_POST['paymentType'] : 'Transfer';

        // Process text fields
        $voucher = isset($_POST['voucher']) ? $_POST['voucher'] : '';
        $agent = isset($_POST['agent']) ? $_POST['agent'] : '';
        $remark = isset($_POST['remark']) ? $_POST['remark'] : '';
        $specialRequest = isset($_POST['specialRequest']) ? (int)$_POST['specialRequest'] : 0;
        $specialRequestNote = isset($_POST['specialRequestNote']) ? $_POST['specialRequestNote'] : '';

        // Process date and zone
        $useDate = $_POST['useDate'];
        $useZone = isset($_POST['useZone']) ? $_POST['useZone'] : '1';
        if (empty($useZone) || $useZone == 'Zone') {
            $useZone = 1;
        }

        // Process tables
        $uTables = $_POST['tables'];

        // Log the original table string for debugging
        file_put_contents("log_tables_original.txt", "Original tables: {$uTables}\n", FILE_APPEND);

        // Remove slashes from table IDs before saving to database
        $uTables = str_replace('/', '', $uTables);

        // Log the modified table string for debugging
        file_put_contents("log_tables_modified.txt", "Tables without slashes: {$uTables}\n", FILE_APPEND);

        // Set cruise ID and user key
        $cruiseId = 1;  // Hardcoded for now
        $zoneId = $useZone;
        $userKey = $_SESSION['userKey'];

        // Insert booking
        $orderNo = insert_booking(
            $customername,
            $phone,
            $adult,
            $child,
            $infant,
            $guide,
            $foc,
            $tl,
            $useDate,
            $useZone,
            $voucher,
            $agent,
            $remark,
            $cruiseId,
            $zoneId,
            $uTables,
            $amount,
            $paymentType,
            $userKey,
            'WP',
            $specialRequest,
            $specialRequestNote
        );

        // Return success response
        echo json_encode([
            'status' => 'success',
            'message' => 'Booking saved successfully',
            'orderNo' => $orderNo,
            'tables' => explode(',', $uTables)
        ]);

    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
} else {
    // Return error for invalid request method
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid request method'
    ]);
}
?>