<?php
/**
 * Test page to verify user creation and role system
 * This page can be accessed without login to test the system
 */

// Include database configuration
require_once 'config/config.php';

try {
    // Connect to the database
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

    // Get all users
    $sql = "SELECT id, user, name, email, role, status, create_date FROM kp_login ORDER BY role DESC, id ASC";
    $stmt = $pdo->query($sql);
    $users = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    $users = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Users - Sawasdee Backend</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .role-super_admin { background-color: #dc3545; color: white; }
        .role-admin { background-color: #fd7e14; color: white; }
        .role-user { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1>User System Test</h1>
                <p class="text-muted">This page shows all users in the system for testing purposes.</p>
                
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?= htmlspecialchars($error) ?>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5>System Users</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                        <div class="alert alert-warning">
                            No users found. Please run the user creation script first.
                            <br><br>
                            <a href="login/create_default_users.php" class="btn btn-primary">
                                Create Default Users
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($user['id']) ?></td>
                                        <td><strong><?= htmlspecialchars($user['user']) ?></strong></td>
                                        <td><?= htmlspecialchars($user['name']) ?></td>
                                        <td><?= htmlspecialchars($user['email']) ?></td>
                                        <td>
                                            <span class="badge role-<?= $user['role'] ?>">
                                                <?= ucfirst(str_replace('_', ' ', $user['role'])) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['status']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('M j, Y H:i', strtotime($user['create_date'])) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Test Login Credentials</h6>
                            </div>
                            <div class="card-body">
                                <h6 class="text-danger">Super Administrator</h6>
                                <p><strong>Username:</strong> superadmin<br>
                                <strong>Password:</strong> superadmin123</p>
                                
                                <h6 class="text-warning">Administrator</h6>
                                <p><strong>Username:</strong> admin<br>
                                <strong>Password:</strong> admin123</p>
                                
                                <h6 class="text-success">User</h6>
                                <p><strong>Username:</strong> user<br>
                                <strong>Password:</strong> user123</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6>Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <a href="login/" class="btn btn-primary mb-2 w-100">
                                    Go to Login Page
                                </a>
                                <a href="login/create_default_users.php" class="btn btn-success mb-2 w-100">
                                    Create/Update Default Users
                                </a>
                                <a href="dashboard.php" class="btn btn-info mb-2 w-100">
                                    Go to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <h6>Role Permissions:</h6>
                    <ul class="mb-0">
                        <li><strong>Super Admin:</strong> Full system access, can manage all users and settings</li>
                        <li><strong>Admin:</strong> Can manage bookings, view reports, manage regular users</li>
                        <li><strong>User:</strong> Can view and create bookings, limited access</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
