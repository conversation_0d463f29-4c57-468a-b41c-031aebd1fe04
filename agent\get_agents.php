<?php
session_start();
require_once '../dbconnect/_dbconnect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    $pdo = db_connect();
    
    // Get all agents
    $stmt = $pdo->prepare("SELECT id, name as agent_name FROM kp_agent ORDER BY name ASC");
    $stmt->execute();
    
    $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($agents);
    
} catch (PDOException $e) {
    error_log("Database error in get_agents.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in get_agents.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching agents']);
}
?>
