<?php
session_start();

echo "<h2>Testing Fixed requireRole Function</h2>";

// Test different scenarios
echo "<h3>Test Scenarios:</h3>";

// Scenario 1: Not logged in
echo "<h4>1. Test when not logged in:</h4>";
session_destroy();
session_start();
try {
    require_once 'includes/role_check.php';
    requireRole('admin');
    echo "<p style='color: green;'>✅ This should not appear - should redirect to login</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

// Scenario 2: Logged in but wrong role
echo "<h4>2. Test when logged in with wrong role:</h4>";
$_SESSION['loggedin'] = true;
$_SESSION['username'] = 'testuser';
$_SESSION['role'] = 'user'; // Not admin
try {
    requireRole('admin');
    echo "<p style='color: green;'>✅ This should not appear - should show access denied</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

// Scenario 3: Logged in with correct role
echo "<h4>3. Test when logged in with correct role:</h4>";
$_SESSION['role'] = 'admin'; // Now admin
try {
    requireRole('admin');
    echo "<p style='color: green;'>✅ Access granted - this should appear</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Now try accessing userManage:</h3>";
echo "<p><a href='userManage/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none;'>Access User Management</a></p>";

echo "<h3>Other useful links:</h3>";
echo "<p><a href='debug_session.php'>Debug Session</a> | ";
echo "<a href='fix_login_issue.php'>Fix Login Issues</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
</style>
