<?php
/**
 * Enhanced Reservations API
 * Returns detailed reservation information including status, customer details, and booking times
 */

// Include database connection
require_once '../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get parameters with defaults
    $useDate = $_GET['useDate'] ?? date('Y-m-d');
    $useZone = $_GET['useZone'] ?? '1';

    // Ensure useZone is not empty
    if (empty($useZone)) {
        $useZone = '1';
    }

    // Get enhanced bookings from database with additional details
    $enhancedBookings = get_enhanced_browser_bookings($useDate, $useZone);
    $reservations = [];

    // Process bookings with enhanced information
    foreach ($enhancedBookings as $booking) {
        if (!empty($booking['tables'])) {
            $tables = explode(',', $booking['tables']);
            foreach ($tables as $table) {
                if (!empty(trim($table))) {
                    $reservations[] = [
                        'table_id' => trim($table),
                        'status' => determineReservationStatus($booking),
                        'customer_name' => $booking['customer_name'] ?? '',
                        'booking_time' => $booking['booking_time'] ?? '',
                        'booking_id' => $booking['id'] ?? '',
                        'payment_status' => $booking['payment_status'] ?? '',
                        'special_request' => $booking['special_request'] ?? '',
                        'adult_count' => $booking['adult'] ?? 0,
                        'child_count' => $booking['child'] ?? 0,
                        'amount' => $booking['amount'] ?? 0
                    ];
                }
            }
        }
    }

    // Return enhanced reservations as JSON
    echo json_encode($reservations);

} catch (Exception $e) {
    // Log error and return empty array
    error_log("Error in get_enhanced_reservations.php: " . $e->getMessage());
    echo json_encode([]);
}

/**
 * Get enhanced browser bookings with additional details
 */
function get_enhanced_browser_bookings($date, $zone) {
    try {
        $conn = db_connect();
        
        $sql = "SELECT 
                    id,
                    tables,
                    customer_name,
                    booking_time,
                    payment_status,
                    special_request,
                    adult,
                    child,
                    amount,
                    created_at,
                    updated_at
                FROM kp_booking 
                WHERE use_date = :date 
                AND use_zone = :zone 
                ORDER BY created_at DESC";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':zone', $zone);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in get_enhanced_browser_bookings: " . $e->getMessage());
        return [];
    }
}

/**
 * Determine reservation status based on booking data
 */
function determineReservationStatus($booking) {
    // Check payment status first
    $paymentStatus = $booking['payment_status'] ?? '';
    
    switch (strtolower($paymentStatus)) {
        case 'paid':
            return 'confirmed';
        case 'wp':
        case 'waiting':
            return 'pending';
        case 'cancelled':
            return 'cancelled';
        default:
            // If no payment status, consider it booked
            return 'booked';
    }
}

/**
 * Get reservation statistics for a specific date and zone
 */
function getReservationStatistics($date, $zone) {
    try {
        $conn = db_connect();
        
        $sql = "SELECT 
                    payment_status,
                    COUNT(*) as count,
                    SUM(adult) as total_adults,
                    SUM(child) as total_children,
                    SUM(amount) as total_amount
                FROM kp_booking 
                WHERE use_date = :date 
                AND use_zone = :zone 
                GROUP BY payment_status";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':zone', $zone);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getReservationStatistics: " . $e->getMessage());
        return [];
    }
}

// If statistics are requested
if (isset($_GET['statistics']) && $_GET['statistics'] === 'true') {
    try {
        $stats = getReservationStatistics($useDate, $useZone);
        echo json_encode([
            'reservations' => $reservations,
            'statistics' => $stats
        ]);
    } catch (Exception $e) {
        error_log("Error getting statistics: " . $e->getMessage());
        echo json_encode(['reservations' => $reservations, 'statistics' => []]);
    }
}
?>
