<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Generate a new CSRF token and store it in the session
 * 
 * @return string The generated CSRF token
 */
function generate_csrf_token() {
    // Generate a random token
    $token = bin2hex(random_bytes(32));
    
    // Store the token in the session
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    return $token;
}

/**
 * Validate a CSRF token against the one stored in the session
 * 
 * @param string $token The token to validate
 * @return bool True if the token is valid, false otherwise
 */
function validate_csrf_token($token) {
    // Check if token exists in session
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // Check if token has expired (30 minutes lifetime)
    if (time() - $_SESSION['csrf_token_time'] > 1800) {
        // Token expired, generate a new one
        generate_csrf_token();
        return false;
    }
    
    // Validate the token
    return hash_equals($_SESSION['csrf_token'], $token);
}
?>
