# Password System Upgrade

This document explains the changes made to the password system and how to update your database to support secure password hashing.

## Changes Made

1. Implemented secure password hashing using the Argon2id algorithm
2. Added automatic password upgrading from plain text to secure hashes
3. Improved error handling and security logging
4. Enhanced session security

## Database Update Required

The current database schema has a `pass` column in the `kp_login` table that is too small (VARCHAR(45)) to store secure password hashes. Secure password hashes require at least VARCHAR(255).

### How to Update the Database

You have two options to update your database:

#### Option 1: Run the PHP Script (Recommended)

1. Navigate to the following URL in your browser:
   ```
   http://your-website.com/dbconnect/update_password_column.php
   ```

2. The script will check your current database schema and make the necessary changes.

3. You should see a success message when the update is complete.

#### Option 2: Run the SQL Script Manually

1. Open your MySQL client (phpMyAdmin, MySQL Workbench, command line, etc.)

2. Run the following SQL commands:

   ```sql
   -- Alter the kp_login table to increase the size of the pass column
   ALTER TABLE `kp_login` 
   MODIFY COLUMN `pass` VARCHAR(255) DEFAULT NULL COMMENT 'Stores password hash (Argon2id)';
   
   -- Optional: Add an email column if it doesn't exist
   -- Only run this if you don't already have an email column
   ALTER TABLE `kp_login` 
   ADD COLUMN `email` VARCHAR(100) DEFAULT NULL AFTER `name`;
   ```

## How the New Password System Works

1. When a user logs in with their existing plain text password, the system will:
   - Verify the password
   - Automatically upgrade it to a secure hash
   - Store the hash in the database

2. All future logins will use the secure hash for verification.

3. If the hashing algorithm or parameters change in the future, the system will automatically rehash passwords as needed.

## Security Benefits

- Passwords are no longer stored in plain text
- Even if the database is compromised, the actual passwords cannot be recovered
- The Argon2id algorithm is resistant to brute force attacks
- Password hashing parameters can be adjusted as hardware improves

## Troubleshooting

If you encounter any issues with the password system:

1. Check the error logs for specific error messages
2. Verify that the database update was successful
3. Ensure that the PHP version is 7.2 or higher (required for Argon2id)

For assistance, please contact the system administrator.
