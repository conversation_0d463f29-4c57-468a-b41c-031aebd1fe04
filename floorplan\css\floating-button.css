/* Floating <PERSON><PERSON> Styles */
.floating-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #ffc107; /* Warning color (yellow) */
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    background-color: #e0a800; /* Darker yellow on hover */
}

.floating-btn i {
    font-size: 24px;
}

.floating-btn-text {
    position: fixed;
    bottom: 30px;
    right: 100px;
    background-color: #ffc107;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: translateX(20px);
    pointer-events: none;
}

.floating-btn:hover + .floating-btn-text {
    opacity: 1;
    transform: translateX(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-btn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }
    
    .floating-btn-text {
        display: none;
    }
}
