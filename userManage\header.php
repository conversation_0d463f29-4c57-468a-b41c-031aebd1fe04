<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="../assets/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="../assets/css/styles.css" />

  <title>Booking</title>

  <!-- Custom CSS for table display and modal -->
  <style>
    /* Custom badge colors */
    .badge.bg-purple {
      background-color: #6f42c1 !important;
      color: white;
    }

    .badge.bg-pink {
      background-color: #e83e8c !important;
      color: white;
    }

    /* Table groups styling */
    .table-groups {
      display: flex;
      flex-direction: column;
      gap: 0.35rem;
      max-width: 100%;
      padding: 0.25rem 0;
    }

    .table-group {
      display: flex;
      flex-wrap: wrap;
      gap: 0.25rem;
      align-items: center;
      padding: 0.15rem 0.25rem;
      border-radius: 4px;
      background-color: rgba(0,0,0,0.02);
    }

    .table-group:hover {
      background-color: rgba(0,0,0,0.05);
    }

    .table-group small.text-muted {
      font-size: 0.75rem;
      font-weight: 600;
      min-width: 60px;
    }

    /* Make badges more readable */
    .badge {
      font-size: 0.85rem;
      padding: 0.35em 0.65em;
      font-weight: 600;
    }

    /* Add hover effect */
    .badge:hover {
      opacity: 0.9;
      transform: scale(1.05);
      transition: all 0.2s ease;
    }

    /* Ensure table cell has enough width */
    .user-table {
      min-width: 120px;
      display: inline-block;
    }

    /* Booking Modal Styles */
    #addContactModal .modal-dialog {
      max-width: 600px;
    }

    #addContactModal .modal-title {
      font-size: 1.25rem;
      font-weight: 600;
    }

    #addContactModal .form-label {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    #addContactModal .form-control {
      padding: 0.5rem 0.75rem;
      border-radius: 4px;
    }

    #addContactModal .modal-footer {
      border-top: 1px solid #dee2e6;
      padding: 1rem;
    }

    #addContactModal .btn-light {
      background-color: #f8f9fa;
      border-color: #dee2e6;
    }

    #addContactModal .btn-primary {
      background-color: #4361ee;
    }

    #selected-tables-display {
      color: #4361ee;
      display: block;
      max-height: 100px;
      overflow-y: auto;
      padding: 8px;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      background-color: #f8f9fa;
      margin-top: 5px;
      word-break: break-all;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .table-group small.text-muted {
        width: 100%;
        margin-bottom: 0.25rem;
      }

      #addContactModal .modal-dialog {
        margin: 0.5rem;
      }
    }
  </style>

</head>