<?php
// Include role checking
require_once 'includes/role_check.php';

// Require login
requireLogin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sawasdee Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1 class="card-title">Welcome, <?php echo isset($_SESSION['name']) && !empty($_SESSION['name']) ? htmlspecialchars($_SESSION['name']) : htmlspecialchars($_SESSION['username']); ?>!</h1>
                        <p class="card-text">You have successfully logged in to the Sawasdee Admin Dashboard.</p>

                        <div class="mb-4">
                            <h4>User Information</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Username:</th>
                                    <td><?php echo htmlspecialchars($_SESSION['username']); ?></td>
                                </tr>
                                <tr>
                                    <th>Name:</th>
                                    <td><?php echo isset($_SESSION['name']) ? htmlspecialchars($_SESSION['name']) : 'Not set'; ?></td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td><?php echo isset($_SESSION['email']) ? htmlspecialchars($_SESSION['email']) : 'Not set'; ?></td>
                                </tr>
                                <tr>
                                    <th>Role:</th>
                                    <td><?= getRoleBadge(getCurrentUserRole()) ?></td>
                                </tr>
                                <tr>
                                    <th>User ID:</th>
                                    <td><?php echo isset($_SESSION['id']) ? htmlspecialchars($_SESSION['id']) : 'Not set'; ?></td>
                                </tr>
                            </table>
                        </div>

                        <div class="mb-4">
                            <h4>Quick Actions</h4>
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="booking/" class="btn btn-primary w-100">
                                        <i class="bi bi-calendar-check"></i> Bookings
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="floorplan/" class="btn btn-success w-100">
                                        <i class="bi bi-diagram-3"></i> Floor Plan
                                    </a>
                                </div>
                                <?php if (hasPermission('user_management')): ?>
                                <div class="col-md-3 mb-2">
                                    <a href="manage/users.php" class="btn btn-warning w-100">
                                        <i class="bi bi-people"></i> Users
                                    </a>
                                </div>
                                <?php endif; ?>
                                <?php if (hasPermission('agent_management')): ?>
                                <div class="col-md-3 mb-2">
                                    <a href="agent/" class="btn btn-info w-100">
                                        <i class="bi bi-person-badge"></i> Agents
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <a href="login/logout.php" class="btn btn-danger">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                        <a href="login/reset_password.php" class="btn btn-warning">
                            <i class="bi bi-key"></i> Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
