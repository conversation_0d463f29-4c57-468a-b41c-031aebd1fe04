<?php
// Display PHP error settings
echo "<h2>PHP Error Settings</h2>";
echo "display_errors: " . ini_get('display_errors') . "<br>";
echo "error_reporting: " . ini_get('error_reporting') . "<br>";
echo "error_log: " . ini_get('error_log') . "<br>";

// Try to read the error log
echo "<h2>Recent Error Log Entries (if accessible)</h2>";
$error_log = ini_get('error_log');
if (file_exists($error_log) && is_readable($error_log)) {
    $log_content = file_get_contents($error_log);
    $lines = array_slice(explode("\n", $log_content), -50); // Get last 50 lines
    echo "<pre>" . htmlspecialchars(implode("\n", $lines)) . "</pre>";
} else {
    echo "Error log file not accessible or not found.";
}
?>
