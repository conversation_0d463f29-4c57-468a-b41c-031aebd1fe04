<?php
/**
 * Role-based access control helper
 * Provides functions to check user permissions and roles
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true;
}

/**
 * Get current user's role
 * @return string|null
 */
function getCurrentUserRole() {
    return $_SESSION['role'] ?? null;
}

/**
 * Get current user's ID
 * @return int|null
 */
function getCurrentUserId() {
    return $_SESSION['id'] ?? null;
}

/**
 * Get current user's name
 * @return string|null
 */
function getCurrentUserName() {
    return $_SESSION['name'] ?? null;
}

/**
 * Check if current user has super admin role
 * @return bool
 */
function isSuperAdmin() {
    return isLoggedIn() && getCurrentUserRole() === 'super_admin';
}

/**
 * Check if current user has admin role or higher
 * @return bool
 */
function isAdmin() {
    $role = getCurrentUserRole();
    return isLoggedIn() && ($role === 'admin' || $role === 'super_admin');
}

/**
 * Check if current user is a regular user
 * @return bool
 */
function isUser() {
    return isLoggedIn() && getCurrentUserRole() === 'user';
}

/**
 * Check if user has permission to access a specific feature
 * @param string $feature Feature name
 * @return bool
 */
function hasPermission($feature) {
    if (!isLoggedIn()) {
        return false;
    }

    $role = getCurrentUserRole();
    
    // Define permissions for each role
    $permissions = [
        'super_admin' => [
            'user_management',
            'system_settings',
            'view_all_bookings',
            'edit_all_bookings',
            'delete_bookings',
            'floor_management',
            'table_management',
            'agent_management',
            'export_data',
            'view_reports'
        ],
        'admin' => [
            'view_all_bookings',
            'edit_all_bookings',
            'delete_bookings',
            'floor_management',
            'table_management',
            'agent_management',
            'export_data',
            'view_reports'
        ],
        'user' => [
            'view_bookings',
            'create_bookings',
            'edit_own_bookings',
            'view_floorplan'
        ]
    ];

    return isset($permissions[$role]) && in_array($feature, $permissions[$role]);
}

/**
 * Require login - redirect to login page if not logged in
 * @param string $redirectUrl URL to redirect to after login
 */
function requireLogin($redirectUrl = null) {
    if (!isLoggedIn()) {
        $loginUrl = '/SawasdeeBackend/login/';
        if ($redirectUrl) {
            $loginUrl .= '?redirect=' . urlencode($redirectUrl);
        }
        header('Location: ' . $loginUrl);
        exit;
    }
}

/**
 * Show access denied page with proper HTML formatting
 * @param string $message Error message to display
 * @param string $currentRole Current user's role
 * @param string|array $requiredRole Required role(s)
 */
function showAccessDeniedPage($message, $currentRole, $requiredRole) {
    $requiredRoleStr = is_array($requiredRole) ? implode(', ', $requiredRole) : $requiredRole;
    $currentRoleDisplay = $currentRole ?: 'None';

    // Clear any previous output
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Access Denied</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f8f9fa;
                margin: 0;
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .error-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                text-align: center;
                max-width: 500px;
                width: 100%;
            }
            .error-icon {
                font-size: 64px;
                color: #dc3545;
                margin-bottom: 20px;
            }
            .error-title {
                color: #dc3545;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 15px;
            }
            .error-message {
                color: #6c757d;
                margin-bottom: 20px;
                line-height: 1.5;
            }
            .role-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                font-size: 14px;
            }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 5px;
            }
            .btn:hover {
                background: #0056b3;
            }
            .btn-secondary {
                background: #6c757d;
            }
            .btn-secondary:hover {
                background: #545b62;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">🚫</div>
            <div class="error-title">Access Denied</div>
            <div class="error-message"><?php echo htmlspecialchars($message); ?></div>

            <div class="role-info">
                <strong>Your Role:</strong> <?php echo htmlspecialchars($currentRoleDisplay); ?><br>
                <strong>Required Role:</strong> <?php echo htmlspecialchars($requiredRoleStr); ?>
            </div>

            <div>
                <a href="/SawasdeeBackend/login/" class="btn">Login with Different Account</a>
                <a href="/SawasdeeBackend/" class="btn btn-secondary">Go to Home</a>
            </div>
        </div>
    </body>
    </html>
    <?php
}

/**
 * Require specific role - redirect or show error if insufficient permissions
 * @param string|array $requiredRole Required role(s)
 * @param string $errorMessage Custom error message
 */
function requireRole($requiredRole, $errorMessage = null) {
    requireLogin();

    $currentRole = getCurrentUserRole();
    $hasAccess = false;

    if (is_array($requiredRole)) {
        $hasAccess = in_array($currentRole, $requiredRole);
    } else {
        // Handle role hierarchy
        switch ($requiredRole) {
            case 'super_admin':
                $hasAccess = ($currentRole === 'super_admin');
                break;
            case 'admin':
                $hasAccess = ($currentRole === 'admin' || $currentRole === 'super_admin');
                break;
            case 'user':
                $hasAccess = true; // All logged-in users have at least user access
                break;
        }
    }

    if (!$hasAccess) {
        // Instead of die(), show a proper error page
        $defaultMessage = 'Access denied. You do not have permission to access this page.';
        $message = $errorMessage ?: $defaultMessage;

        // Show a proper HTML error page instead of just dying
        showAccessDeniedPage($message, $currentRole, $requiredRole);
        exit;
    }
}

/**
 * Get role display name
 * @param string $role Role code
 * @return string
 */
function getRoleDisplayName($role) {
    $roleNames = [
        'super_admin' => 'Super Administrator',
        'admin' => 'Administrator',
        'user' => 'User'
    ];
    
    return $roleNames[$role] ?? ucfirst($role);
}

/**
 * Get role badge HTML
 * @param string $role Role code
 * @return string
 */
function getRoleBadge($role) {
    $badges = [
        'super_admin' => '<span class="badge bg-danger">Super Admin</span>',
        'admin' => '<span class="badge bg-warning">Admin</span>',
        'user' => '<span class="badge bg-success">User</span>'
    ];
    
    return $badges[$role] ?? '<span class="badge bg-secondary">' . ucfirst($role) . '</span>';
}

/**
 * Check if current user can manage other users
 * @return bool
 */
function canManageUsers() {
    return hasPermission('user_management');
}

/**
 * Check if current user can edit a specific user
 * @param int $targetUserId Target user ID
 * @param string $targetUserRole Target user role
 * @return bool
 */
function canEditUser($targetUserId, $targetUserRole) {
    $currentRole = getCurrentUserRole();
    $currentUserId = getCurrentUserId();
    
    // Users can edit themselves
    if ($currentUserId == $targetUserId) {
        return true;
    }
    
    // Super admin can edit anyone
    if ($currentRole === 'super_admin') {
        return true;
    }
    
    // Admin can edit users but not other admins or super admins
    if ($currentRole === 'admin' && $targetUserRole === 'user') {
        return true;
    }
    
    return false;
}

/**
 * Log user action
 * @param string $action Action performed
 * @param string $details Additional details
 */
function logUserAction($action, $details = '') {
    $username = $_SESSION['username'] ?? 'Unknown';
    $userId = $_SESSION['id'] ?? 0;
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    $logMessage = "[" . date('Y-m-d H:i:s') . "] User: {$username} (ID: {$userId}) | Action: {$action} | IP: {$ip}";
    if ($details) {
        $logMessage .= " | Details: {$details}";
    }
    
    error_log($logMessage);
}
?>
