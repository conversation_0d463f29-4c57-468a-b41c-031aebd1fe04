/**
 * Floor Switcher Script
 * This script handles the switching between different floor plans
 * and ensures the correct images and tables are displayed.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize floor tabs
    initializeFloorTabs();

    // Load initial floor data
    loadInitialFloorData();
});

/**
 * Initialize the floor tabs with event listeners
 */
function initializeFloorTabs() {
    // Get all floor tab links
    const floorTabs = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');

    // Add click event listeners to each tab
    floorTabs.forEach(tab => {
        tab.addEventListener('click', function(event) {
            const floorId = this.getAttribute('href').replace('#', '');
            const floorNumber = floorId.replace('floor', '');

            // Update the current floor in the session storage
            sessionStorage.setItem('currentFloor', floorNumber);

            // Load the floor data
            loadFloorData(floorNumber);

            // Update the UI to reflect the current floor
            updateFloorUI(floorNumber);
        });
    });
}

/**
 * Load the initial floor data based on the active tab
 */
function loadInitialFloorData() {
    // Get the active tab
    const activeTab = document.querySelector('.nav-link.active[data-bs-toggle="tab"]');
    if (activeTab) {
        const floorId = activeTab.getAttribute('href').replace('#', '');
        const floorNumber = floorId.replace('floor', '');

        // Store the current floor in session storage
        sessionStorage.setItem('currentFloor', floorNumber);

        // Load the floor data
        loadFloorData(floorNumber);

        // Update the UI
        updateFloorUI(floorNumber);
    }
}

/**
 * Load the floor data for the specified floor
 * @param {string} floorNumber - The floor number (1, 2, or 3)
 */
function loadFloorData(floorNumber) {
    // Get the selected date
    const selectedDate = document.getElementById('myDate')?.value || '';

    try {
        // Use the functions even if they're just placeholders
        // The error-handler.js file provides basic implementations if they're not defined yet
        const formattedDate = window.formatDateForAPI ? window.formatDateForAPI(selectedDate) : selectedDate;

        // Load booked tables for the selected floor
        if (window.loadBookedTables) {
            window.loadBookedTables(formattedDate, floorNumber);
        } else {
            console.log('loadBookedTables function not available yet');
            // Try again after a short delay
            setTimeout(function() {
                loadFloorData(floorNumber);
            }, 500);
        }
    } catch (e) {
        console.error('Error in loadFloorData:', e);
        // Try again after a short delay
        setTimeout(function() {
            loadFloorData(floorNumber);
        }, 1000);
    }

    // Update the zone input in the booking form
    const useZoneElement = document.getElementById('useZone');
    if (useZoneElement) {
        useZoneElement.value = floorNumber;
    }
}

/**
 * Update the UI to reflect the current floor
 * @param {string} floorNumber - The floor number (1, 2, or 3)
 */
function updateFloorUI(floorNumber) {
    // Update the floor title
    const floorTitle = document.getElementById('floorTitle');
    if (floorTitle) {
        floorTitle.textContent = `Floor ${floorNumber}`;
    }

    // Update any other UI elements that need to reflect the current floor
    // For example, you might want to show/hide certain buttons or controls

    // Update the floor plan image based on the floor number
    updateFloorPlanImage(floorNumber);

    // Update the table colors based on the floor
    updateTableColors(floorNumber);
}

/**
 * Update the floor plan image based on the floor number
 * @param {string} floorNumber - The floor number (1, 2, or 3)
 */
function updateFloorPlanImage(floorNumber) {
    // Get the floor element
    const floorId = `floor${floorNumber}`;
    const floorElement = document.getElementById(floorId);
    if (!floorElement) return;

    // Find the image element within the SVG
    const svgElement = floorElement.querySelector('svg');
    if (!svgElement) return;

    const imageElement = svgElement.querySelector('image');
    if (!imageElement) return;

    // Set the appropriate image based on the floor number
    let imagePath = '../assets/images/floorplan/Floor_Plan_1.4.png?v=4.2';
    if (floorNumber === '2') {
        imagePath = '../assets/images/floorplan/Floor_Plan_2.5.png?v=5.5';
    } else if (floorNumber === '3') {
        imagePath = '../assets/images/floorplan/Floor_Plan_3.4.png?v=3.4';
    }

    // Update the image href
    imageElement.setAttribute('href', imagePath);
}

/**
 * Update the table colors based on the floor
 * @param {string} floorNumber - The floor number (1, 2, or 3)
 */
function updateTableColors(floorNumber) {
    try {
        // Define colors for each floor with enhanced reservation status colors
        const floorColors = {
            '1': {
                'C': '#dc3545', // Red
                'V': '#12deb9'  // Green For VIP rooms
            },
            '2': {
                'B': '#ffc107', // Yellow
                'H': '#6f42c1', // Purple
                'V': '#12deb9'  // Green For VIP rooms
            },
            '3': {
                'A': '#539bff' // Blue
            }
        };

        // Enhanced reservation status colors
        const reservationColors = {
            'available': null, // Use floor-specific colors
            'selected': '#00FF00', // Bright Green
            'booked': '#cccccc', // Gray
            'disabled': '#999999', // Dark Gray
            'pending': '#ff9800', // Orange for pending reservations
            'confirmed': '#4caf50', // Green for confirmed reservations
            'cancelled': '#f44336' // Red for cancelled reservations
        };

        // Get all table rectangles in the current floor
        const floorId = `floor${floorNumber}`;
        const floorElement = document.getElementById(floorId);
        if (!floorElement) {
            console.log(`Floor element not found: ${floorId}`);
            return;
        }

        const tableRects = floorElement.querySelectorAll('rect[id^="rbox-"]');
        if (tableRects.length === 0) {
            console.log(`No table rectangles found in floor ${floorNumber}`);
        }

        // Update the color of each table based on its row and reservation status
        tableRects.forEach(rect => {
            try {
                const id = rect.getAttribute('id');
                if (!id) {
                    console.warn('Table rectangle has no id attribute');
                    return;
                }

                const tableId = id.replace('rbox-', '');
                const parentElement = rect.parentElement;

                // Determine reservation status
                let reservationStatus = 'available';
                if (parentElement) {
                    if (parentElement.classList.contains('booked-disabled')) {
                        reservationStatus = 'booked';
                    } else if (parentElement.classList.contains('table-disabled')) {
                        reservationStatus = 'disabled';
                    } else if (parentElement.getAttribute('data-value') === '1') {
                        reservationStatus = 'selected';
                    } else if (parentElement.classList.contains('pending-reservation')) {
                        reservationStatus = 'pending';
                    } else if (parentElement.classList.contains('confirmed-reservation')) {
                        reservationStatus = 'confirmed';
                    } else if (parentElement.classList.contains('cancelled-reservation')) {
                        reservationStatus = 'cancelled';
                    }
                }

                // Apply color based on reservation status first
                if (reservationStatus !== 'available' && reservationColors[reservationStatus]) {
                    rect.setAttribute('fill', reservationColors[reservationStatus]);
                    return;
                }

                // For available tables, apply floor-specific colors
                let rowIdentifier;

                // For new format (A1/1, A2/2, etc.)
                if (tableId.includes('/')) {
                    rowIdentifier = tableId.split('/')[0].charAt(0); // A, B, etc.
                }
                // For format with floor number prefix (2H01, 3E02, etc.)
                else if (tableId.length >= 3 && /^\d/.test(tableId)) {
                    // First check if the first character (floor number) matches the current floor
                    if (tableId.charAt(0) === floorNumber) {
                        rowIdentifier = tableId.charAt(1); // H, E, etc.
                    } else {
                        // If not, use the floor number as a fallback
                        rowIdentifier = tableId.charAt(0); // 2, 3, etc.
                    }
                }
                // For old format (A1, B2, etc.)
                else {
                    rowIdentifier = tableId.charAt(0); // A, B, etc.
                }

                // Get the color for this row in this floor
                const color = floorColors[floorNumber] && floorColors[floorNumber][rowIdentifier];

                // Apply the color if it exists
                if (color) {
                    rect.setAttribute('fill', color);
                } else {
                    // Use a default color if no specific color is defined
                    rect.setAttribute('fill', '#539bff');
                }
            } catch (e) {
                console.error('Error processing table rectangle:', e);
            }
        });
    } catch (e) {
        console.error('Error in updateTableColors:', e);
    }
}

/**
 * Enhanced table reservation marking with additional status support
 * @param {Array} reservations - Array of reservation objects with status
 * @param {string} floorNumber - The floor number
 */
function markTableReservations(reservations, floorNumber) {
    try {
        console.log('Marking table reservations for floor:', floorNumber, 'Count:', reservations.length);

        // Clear previous reservation markers
        clearReservationMarkers(floorNumber);

        // Mark each reservation
        reservations.forEach(reservation => {
            try {
                const tableId = reservation.table_id || reservation.id;
                const status = reservation.status || 'booked';
                const customerName = reservation.customer_name || '';
                const bookingTime = reservation.booking_time || '';

                markSingleTableReservation(tableId, status, {
                    customerName,
                    bookingTime,
                    floorNumber
                });
            } catch (e) {
                console.error('Error marking individual reservation:', e);
            }
        });

        // Update floor occupancy
        if (window.FloorOccupancy && window.FloorOccupancy.updateFloorOccupancy) {
            window.FloorOccupancy.updateFloorOccupancy(floorNumber, reservations.length);
        }
    } catch (e) {
        console.error('Error in markTableReservations:', e);
    }
}

/**
 * Mark a single table reservation with enhanced status support
 * @param {string} tableId - The table ID
 * @param {string} status - The reservation status
 * @param {Object} details - Additional reservation details
 */
function markSingleTableReservation(tableId, status, details = {}) {
    try {
        const selectors = getTableSelectors(tableId);
        const $box = $(selectors.box);
        const $rect = $(selectors.rect);

        if (!$box.length || !$rect.length) {
            console.warn(`Table element not found: ${tableId}`);
            return;
        }

        // Remove existing reservation classes
        $box.removeClass('booked-disabled table-disabled pending-reservation confirmed-reservation cancelled-reservation');
        $box.removeAttr('data-booked data-reservation-status');
        $box.find('.reservation-label').remove();

        // Apply status-specific styling
        switch (status) {
            case 'booked':
            case 'confirmed':
                $box.addClass('booked-disabled');
                $box.attr('data-booked', 'true');
                $box.attr('data-reservation-status', status);
                $rect.attr('fill', status === 'confirmed' ? '#4caf50' : '#cccccc');
                addReservationLabel($box, 'BOOKED', details);
                $box.off('click');
                break;

            case 'pending':
                $box.addClass('pending-reservation');
                $box.attr('data-reservation-status', 'pending');
                $rect.attr('fill', '#ff9800');
                addReservationLabel($box, 'PENDING', details);
                break;

            case 'cancelled':
                $box.addClass('cancelled-reservation');
                $box.attr('data-reservation-status', 'cancelled');
                $rect.attr('fill', '#f44336');
                addReservationLabel($box, 'CANCELLED', details);
                break;

            case 'disabled':
                $box.addClass('table-disabled');
                $box.attr('data-reservation-status', 'disabled');
                $rect.attr('fill', '#999999');
                addReservationLabel($box, 'DISABLED', details);
                $box.off('click');
                break;
        }

        console.log(`Marked table ${tableId} as ${status}`);
    } catch (e) {
        console.error('Error marking single table reservation:', e);
    }
}

/**
 * Add reservation label with enhanced information
 * @param {jQuery} $box - The table box element
 * @param {string} labelText - The label text
 * @param {Object} details - Additional details
 */
function addReservationLabel($box, labelText, details = {}) {
    try {
        if (!$box.find('.reservation-label').length) {
            const $label = $('<div class="reservation-label"></div>');
            $label.text(labelText);

            // Add tooltip with additional information
            if (details.customerName || details.bookingTime) {
                let tooltipText = '';
                if (details.customerName) tooltipText += `Customer: ${details.customerName}\n`;
                if (details.bookingTime) tooltipText += `Time: ${details.bookingTime}`;

                $label.attr('title', tooltipText);
                $label.attr('data-bs-toggle', 'tooltip');
                $label.attr('data-bs-placement', 'top');
            }

            $box.append($label);
        }
    } catch (e) {
        console.error('Error adding reservation label:', e);
    }
}

/**
 * Clear all reservation markers for a specific floor
 * @param {string} floorNumber - The floor number
 */
function clearReservationMarkers(floorNumber) {
    try {
        const floorId = `floor${floorNumber}`;
        const floorElement = document.getElementById(floorId);
        if (!floorElement) return;

        const $tables = $(floorElement).find('.svg-box');
        $tables.removeClass('booked-disabled table-disabled pending-reservation confirmed-reservation cancelled-reservation');
        $tables.removeAttr('data-booked data-reservation-status');
        $tables.find('.reservation-label, .booked-label').remove();

        // Reset click handlers
        $tables.off('click').on('click', function(event) {
            if (window.TableSelection && window.TableSelection.handleTableClick) {
                window.TableSelection.handleTableClick.call(window.TableSelection, event);
            }
        });
    } catch (e) {
        console.error('Error clearing reservation markers:', e);
    }
}

/**
 * Get table selectors for different table ID formats
 * @param {string} tableId - The table ID
 * @returns {Object} - Object with box and rect selectors
 */
function getTableSelectors(tableId) {
    if (tableId.includes('/')) {
        const escapedTable = tableId.replace('/', '\\/');
        return {
            box: `#box-${escapedTable}`,
            rect: `#rbox-${escapedTable}`
        };
    }
    return {
        box: `#box-${tableId}`,
        rect: `#rbox-${tableId}`
    };
}

/**
 * Format date for API calls (YYYY-MM-DD)
 * @param {string} dateString - The date string to format
 * @returns {string} - The formatted date string
 */
// formatDateForAPI is defined in footer.php

/**
 * Load booked tables from the server
 * @param {string} date - The date in YYYY-MM-DD format
 * @param {string} zone - The zone/floor number
 */
// loadBookedTables is defined in footer.php

/**
 * Mark tables that are already booked
 * @param {Array} bookedTables - Array of booked table IDs
 */
// markBookedTables is defined in footer.php

/**
 * Load disabled tables from the server
 * @param {string} zone - The zone/floor number
 */
// loadDisabledTables is defined in footer.php

/**
 * Mark tables that are disabled
 * @param {Array} disabledTables - Array of disabled table objects
 * @param {string} zone - The zone/floor number
 */
// markDisabledTables is defined in footer.php

/**
 * Update floor occupancy percentage and progress bar
 * @param {string} floorNumber - The floor number (1, 2, or 3)
 * @param {number} bookedCount - The number of booked tables
 */
// updateFloorOccupancy is defined in footer.php

// Global variable to store selected tables is defined in footer.php
// We're using the existing selectedBoxes variable

/**
 * Toggle the visibility of the booking button
 */
// toggleBookingButton is defined in footer.php

// Add event listener for date change
document.addEventListener('DOMContentLoaded', function() {
    // The date change event is now handled by the datepicker-init.js script
    // This ensures that the datepicker works correctly and triggers the appropriate events

    // Initialize the floor data when the page loads
    // We need to wait for the footer.php scripts to load first
    setTimeout(function() {
        try {
            // Use the functions even if they're just placeholders
            // The error-handler.js file provides basic implementations if they're not defined yet
            const currentFloor = sessionStorage.getItem('currentFloor') || '1';
            const selectedDate = $('#myDate').val() || '';

            // Format the date using our function or a fallback
            const formattedDate = window.formatDateForAPI ? window.formatDateForAPI(selectedDate) : selectedDate;

            // Load booked tables for the selected date and floor
            if (window.loadBookedTables) {
                window.loadBookedTables(formattedDate, currentFloor);
            } else {
                console.log('loadBookedTables function not available yet in DOMContentLoaded');
                // Try again after a short delay
                setTimeout(function() {
                    const newFormattedDate = window.formatDateForAPI ? window.formatDateForAPI(selectedDate) : selectedDate;
                    if (window.loadBookedTables) {
                        window.loadBookedTables(newFormattedDate, currentFloor);
                    }
                }, 2000);
            }
        } catch (e) {
            console.error('Error in DOMContentLoaded floor-switcher.js:', e);
        }
    }, 1000); // Wait 1 second for other scripts to load
});
