<?php
/**
 * Update Profile Handler
 * 
 * Processes profile update requests
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/User.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit;
}

// Validate CSRF token
$token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
if (!Session::validateCsrfToken($token)) {
    header('Location: index.php?error=invalid_token');
    exit;
}

// Get user ID from session
$userId = Session::get('id');

// Create User model instance
$userModel = new User();

// Get current user data
$userData = $userModel->getUserById($userId);
if (!$userData) {
    header('Location: ../login/logout.php');
    exit;
}

// Validate and sanitize inputs
$name = isset($_POST['name']) ? Security::sanitizeInput($_POST['name']) : '';
$email = isset($_POST['email']) ? Security::sanitizeInput($_POST['email']) : '';
$currentPassword = isset($_POST['current_password']) ? $_POST['current_password'] : '';
$newPassword = isset($_POST['new_password']) ? $_POST['new_password'] : '';
$confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

// Validate required fields
if (empty($name) || empty($email)) {
    header('Location: index.php?error=missing_fields');
    exit;
}

// Validate email
if (!Security::validateEmail($email)) {
    header('Location: index.php?error=invalid_email');
    exit;
}

// Prepare user data for update
$updateData = [
    'name' => $name,
    'email' => $email
];

// Handle password change if requested
if (!empty($newPassword) || !empty($confirmPassword) || !empty($currentPassword)) {
    // Check if all password fields are filled
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        header('Location: index.php?error=missing_password_fields');
        exit;
    }
    
    // Check if new passwords match
    if ($newPassword !== $confirmPassword) {
        header('Location: index.php?error=password_mismatch');
        exit;
    }
    
    // Verify current password
    if (!Security::verifyPassword($currentPassword, $userData['pass'])) {
        header('Location: index.php?error=current_password');
        exit;
    }
    
    // Add new password to update data
    $updateData['pass'] = $newPassword;
}

// Update user profile
$result = $userModel->updateUser($userId, $updateData);

if ($result) {
    // Update session data
    Session::set('name', $name);
    Session::set('email', $email);
    
    // Log the action
    error_log("User {$userData['user']} updated their profile");
    
    // Redirect to success page
    header('Location: index.php?success=1');
} else {
    // Redirect to error page
    header('Location: index.php?error=update_failed');
}
exit;
