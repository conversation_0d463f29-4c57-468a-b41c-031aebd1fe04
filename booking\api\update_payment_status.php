<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Handle both JSON and FormData requests
$input = [];
$uploadedFile = null;

if (isset($_POST['booking_id'])) {
    // FormData request (with file upload)
    $input = $_POST;
    $uploadedFile = isset($_FILES['payment_reference']) ? $_FILES['payment_reference'] : null;
    error_log("Payment status update request - FormData: " . json_encode($_POST));
} else {
    // JSON request (backward compatibility)
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    error_log("Payment status update request - JSON: " . $rawInput);
}

// Validate required fields
if (!isset($input['booking_id']) || !isset($input['payment_status'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields', 'received' => $input]);
    exit;
}

$bookingId = intval($input['booking_id']);
$paymentStatus = trim($input['payment_status']);
$note = isset($input['note']) ? trim($input['note']) : '';
$referenceImage = null;

// Validate payment status
$allowedStatuses = ['WP', 'Paid'];
if (!in_array($paymentStatus, $allowedStatuses)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid payment status']);
    exit;
}

// Handle file upload if present
if ($uploadedFile && $uploadedFile['error'] === UPLOAD_ERR_OK) {
    // Validate file
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($uploadedFile['type'], $allowedTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.']);
        exit;
    }

    if ($uploadedFile['size'] > $maxSize) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 5MB.']);
        exit;
    }

    // Create upload directory if it doesn't exist
    $uploadDir = '../../uploads/payment_references/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $fileExtension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
    $fileName = 'payment_' . $bookingId . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;

    // Move uploaded file
    if (move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
        $referenceImage = '../uploads/payment_references/' . $fileName;
        error_log("Payment reference image uploaded: " . $referenceImage);
    } else {
        error_log("Failed to move uploaded file");
    }
}

try {
    // Get database connection
    $pdo = db_connect();

    // Check and add required columns if they don't exist
    $columnsToCheck = [
        'payment_status' => "VARCHAR(10) DEFAULT 'WP' COMMENT 'Payment status: WP=Waiting Payment, Paid=Paid'",
        'payment_note' => "TEXT DEFAULT NULL COMMENT 'Payment note or comment'",
        'attactfile' => "VARCHAR(255) DEFAULT NULL COMMENT 'Attachment file path'"
    ];

    foreach ($columnsToCheck as $columnName => $columnDefinition) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'kp_booking' AND column_name = ?");
        $stmt->execute([$columnName]);
        $columnExists = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

        if (!$columnExists) {
            $pdo->exec("ALTER TABLE kp_booking ADD COLUMN {$columnName} {$columnDefinition}");
            error_log("Added {$columnName} column to kp_booking table");
        }
    }

    // Check if booking exists
    $stmt = $pdo->prepare("SELECT booking_id, orderNo, name FROM kp_booking WHERE booking_id = ?");
    $stmt->execute([$bookingId]);
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$booking) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // Update payment status, note, and attachment file
    $updateFields = ['payment_status = ?'];
    $updateValues = [$paymentStatus];

    if ($note) {
        $updateFields[] = 'payment_note = ?';
        $updateValues[] = $note;
    }

    if ($referenceImage) {
        $updateFields[] = 'attactfile = ?';
        $updateValues[] = $referenceImage;
    }

    $updateValues[] = $bookingId; // for WHERE clause

    $sql = "UPDATE kp_booking SET " . implode(', ', $updateFields) . " WHERE booking_id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($updateValues);
    
    if ($result) {
        // Check if session variables exist before using them
        $username = isset($_SESSION['username']) ? $_SESSION['username'] : (isset($_SESSION['user']) ? $_SESSION['user'] : 'Unknown');
        $userId = isset($_SESSION['id']) ? $_SESSION['id'] : null;

        // Log the payment status change
        $logMessage = "Payment status updated for Booking ID: {$bookingId}, Order: {$booking['orderNo']}, Customer: {$booking['name']}, Status: {$paymentStatus}";
        if ($note) {
            $logMessage .= ", Note: {$note}";
        }
        if ($referenceImage) {
            $logMessage .= ", Reference Image: {$referenceImage}";
        }
        $logMessage .= ", Updated by: {$username} (ID: {$userId})";
        error_log($logMessage);

        echo json_encode([
            'success' => true,
            'message' => 'Payment status updated successfully',
            'booking_id' => $bookingId,
            'payment_status' => $paymentStatus,
            'reference_image' => $referenceImage,
            'note' => $note
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update payment status']);
    }
    
} catch (Exception $e) {
    error_log("Error updating payment status: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);

    // For debugging, include the actual error message
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
