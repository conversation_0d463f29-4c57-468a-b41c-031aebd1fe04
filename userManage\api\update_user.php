<?php
session_start();
require_once '../../includes/role_check.php';
require_once '../../dbconnect/_dbconnect.php';

// Check if user is logged in and has admin access
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Validate required fields
$required_fields = ['id', 'username', 'role', 'status'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => ucfirst($field) . ' is required']);
        exit;
    }
}

$userId = (int)$_POST['id'];
$username = trim($_POST['username']);
$email = isset($_POST['email']) ? trim($_POST['email']) : null;
$name = isset($_POST['name']) ? trim($_POST['name']) : null;
$role = $_POST['role'];
$status = (int)$_POST['status'];
$password = isset($_POST['password']) && !empty($_POST['password']) ? $_POST['password'] : null;

// Validate role permissions
if ($_SESSION['role'] !== 'super_admin' && in_array($role, ['super_admin', 'admin'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'You do not have permission to assign admin roles']);
    exit;
}

// Validate role values
$valid_roles = ['user'];
if ($_SESSION['role'] === 'super_admin') {
    $valid_roles = ['super_admin', 'admin', 'user'];
}

if (!in_array($role, $valid_roles)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid role specified']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();

    // Check if user exists
    $stmt = $pdo->prepare("SELECT id, user as username FROM kp_login WHERE id = ?");
    $stmt->execute([$userId]);
    $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$existingUser) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Check if username already exists (excluding current user)
    $stmt = $pdo->prepare("SELECT id FROM kp_login WHERE user = ? AND id != ?");
    $stmt->execute([$username, $userId]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Username already exists']);
        exit;
    }
    
    // Check if email already exists (excluding current user, if provided)
    if ($email) {
        $stmt = $pdo->prepare("SELECT id FROM kp_login WHERE email = ? AND id != ?");
        $stmt->execute([$email, $userId]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            exit;
        }
    }
    
    // Prepare update query
    if ($password) {
        // Hash new password using Argon2id
        $hashedPassword = password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3,
        ]);
        
        $stmt = $pdo->prepare("UPDATE kp_login SET user = ?, pass = ?, email = ?, name = ?, role = ?, status = ? WHERE id = ?");
        $result = $stmt->execute([$username, $hashedPassword, $email, $name, $role, $status, $userId]);
    } else {
        // Update without changing password
        $stmt = $pdo->prepare("UPDATE kp_login SET user = ?, email = ?, name = ?, role = ?, status = ? WHERE id = ?");
        $result = $stmt->execute([$username, $email, $name, $role, $status, $userId]);
    }
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'User updated successfully'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update user']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
