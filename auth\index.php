<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('location:../login');
    exit;
}

// print_r($_SESSION);
// Array
// (
//     [lg] => th
//     [loggedin] => 1
//     [username] => Test1
//     [password] => Test1
//     [id] => 1
//     [role] => 1
//     [name] => MrKoPi
//     [email] => 
//     [userKey] => kopiko123456
//     [userStatus] => 1
// )

// 1 = admin
// 2 = user
if($_SESSION['role'] == 1){
    $userRole = "Admin";
    $_SESSION['userRole'] = $userRole;
    header('location:../dashboard');
    exit;
}else if($_SESSION['role'] == 2){
    $userRole = "User";
    $_SESSION['userRole'] = $userRole;
    header('location:../dashboard');
    exit;
}else{
    header('location:../login');
    exit;
}
?>
