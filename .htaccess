# Disable directory browsing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Den<PERSON> from all
</Files>

# Protect config files
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect sensitive files
<FilesMatch "\.(sql|log|ini|json|config|env|example|md|gitignore|htaccess|htpasswd)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Allow access to specific JSON files
<FilesMatch "^(jsonBookingBy_|json_by_zone).*\.json$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Set default character set
AddDefaultCharset UTF-8

# Enable CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' https://cdn.jsdelivr.net; connect-src 'self';"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Feature-Policy "camera 'none'; microphone 'none'; geolocation 'none'"
</IfModule>

# Redirect to HTTPS (uncomment when using HTTPS)
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Hide PHP version
    php_flag expose_php Off
    
    # Disable remote file inclusion
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    
    # Disable register globals
    php_flag register_globals Off
    
    # Disable magic quotes
    php_flag magic_quotes_gpc Off
    
    # Set maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Set session security
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1
    php_value session.cookie_secure 0
    
    # Set error reporting
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_error.log
</IfModule>

# Prevent access to PHP error logs
<Files php_error.log>
    Order Allow,Deny
    Deny from all
</Files>

# Prevent PHP execution in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# Prevent access to cache directory
<Directory "cache">
    Order Allow,Deny
    Deny from all
</Directory>

# Prevent browser caching for sensitive files
<FilesMatch "\.(php|html|htm)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "private, no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </IfModule>
</FilesMatch>

# Enable browser caching for static files
<FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$">
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </IfModule>
    <IfModule mod_headers.c>
        Header set Cache-Control "public, max-age=2592000"
    </IfModule>
</FilesMatch>

# Compress text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>
