<?php
/**
 * Database Class
 *
 * Handles all database operations using PDO
 */
class Database {
    private static $instance = null;
    private $conn;

    /**
     * Constructor - Connect to the database
     */
    private function __construct() {
        try {
            // Include config file if not already included
            if (!defined('DB_HOST')) {
                require_once dirname(__DIR__) . '/config/config.php';
            }

            // Create DSN
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;

            // Set PDO options
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            // Create PDO instance
            $this->conn = new PDO($dsn, DB_USER, DB_PASS, $options);

        } catch (PDOException $e) {
            // Log error but don't expose details
            error_log("Database connection error: " . $e->getMessage());
            throw new Exception("Database connection failed. Please try again later.");
        }
    }

    /**
     * Get database instance (Singleton pattern)
     *
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get database connection
     *
     * @return PDO
     */
    public function getConnection() {
        return $this->conn;
    }

    /**
     * Execute a query with parameters
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return PDOStatement
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query error: " . $e->getMessage() . " - SQL: " . $sql);
            throw new Exception("Database query failed. Please try again later.");
        }
    }

    /**
     * Get a single row from the database
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array|null Row data or null if not found
     */
    public function getRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Get multiple rows from the database
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array Rows data
     */
    public function getRows($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Get a single value from the database
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return mixed Value or null if not found
     */
    public function getValue($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        return $row ? $row[0] : null;
    }

    /**
     * Insert data into the database
     *
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @return int|string Last insert ID
     */
    public function insert($table, $data) {
        // Build column and placeholder lists
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));

        // Build SQL query
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";

        // Execute query
        $this->query($sql, array_values($data));

        // Return last insert ID
        return $this->conn->lastInsertId();
    }

    /**
     * Update data in the database
     *
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @param string $where WHERE clause
     * @param array $params Parameters for WHERE clause
     * @return int Number of affected rows
     */
    public function update($table, $data, $where, $params = []) {
        // Build SET clause
        $set = [];
        foreach ($data as $column => $value) {
            $set[] = "{$column} = ?";
        }
        $set = implode(', ', $set);

        // Build SQL query
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";

        // Combine data values and where params
        $params = array_merge(array_values($data), $params);

        // Execute query
        $stmt = $this->query($sql, $params);

        // Return number of affected rows
        return $stmt->rowCount();
    }

    /**
     * Delete data from the database
     *
     * @param string $table Table name
     * @param string $where WHERE clause
     * @param array $params Parameters for WHERE clause
     * @return int Number of affected rows
     */
    public function delete($table, $where, $params = []) {
        // Build SQL query
        $sql = "DELETE FROM {$table} WHERE {$where}";

        // Execute query
        $stmt = $this->query($sql, $params);

        // Return number of affected rows
        return $stmt->rowCount();
    }

    /**
     * Begin a transaction
     */
    public function beginTransaction() {
        $this->conn->beginTransaction();
    }

    /**
     * Commit a transaction
     */
    public function commit() {
        $this->conn->commit();
    }

    /**
     * Rollback a transaction
     */
    public function rollback() {
        $this->conn->rollBack();
    }
}
