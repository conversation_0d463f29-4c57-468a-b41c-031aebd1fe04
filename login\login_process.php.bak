<?php
// Backup of the original login_process.php file
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/models/User.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = isset($_POST['username']) ? Security::sanitizeInput($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : ''; // Don't sanitize password

    // Validate CSRF token
    $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';

    if (!Session::validateCsrfToken($token)) {
        // Invalid CSRF token
        $_SESSION['login_error'] = 'Invalid request. Please try again.';
        header('Location: index.php');
        exit;
    }

    // Check rate limiting
    $ipAddress = $_SERVER['REMOTE_ADDR'];
    if (!Security::checkRateLimit($ipAddress . '_login', 5, 300)) {
        // Rate limit exceeded
        $_SESSION['login_error'] = 'Too many login attempts. Please try again later.';
        header('Location: index.php');
        exit;
    }

    // Validate input
    if (empty($username) || empty($password)) {
        $_SESSION['login_error'] = 'Please enter both username and password.';
        header('Location: index.php');
        exit;
    }


    // Authenticate user
    $user = new User();
    $userData = $user->authenticate($username, $password);

    if ($userData) {
        // Start session
        Session::start();

        // Regenerate session ID to prevent session fixation attacks
        session_regenerate_id(true);

        // Set session variables
        Session::set('loggedin', true);
        Session::set('username', $username);
        Session::set('role', $userData['role']);
        Session::set('id', $userData['id']);
        Session::set('name', $userData['name']);
        Session::set('email', $userData['email']);
        Session::set('userKey', $userData['user_key']);
        Session::set('userStatus', $userData['status']);

        // Set last activity time for session timeout
        Session::set('last_activity', time());

        // Log successful login
        error_log("Successful login: User {$username} from IP {$ipAddress}");

        // Redirect to dashboard
        header('Location: ../index.php');
        exit;
    } else {
        // Invalid credentials
        $_SESSION['login_error'] = 'Invalid username or password.';
        header('Location: index.php');
        exit;
    }
} else {
    // Not a POST request - redirect with error message
    $_SESSION['login_error'] = 'Invalid access method. Please use the login form.';
    header('Location: index.php');
    exit;
}
