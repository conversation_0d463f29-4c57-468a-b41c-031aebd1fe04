<?php
// Set proper error reporting but don't display errors to users
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(array('status' => 'error', 'message' => 'Unauthorized access'));
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(array('status' => 'error', 'message' => 'Invalid request method'));
    exit;
}

try {
    // Check if ID is provided
    if (!isset($_POST['id'])) {
        throw new Exception('Booking ID is required');
    }

    // Sanitize and validate ID
    $id = $_POST['id'];
    $id = intval($id); // Convert to integer

    // Additional validation
    if ($id <= 0) {
        throw new Exception('Invalid booking ID');
    }

    // Include database connection
    include_once("../dbconnect/_dbconnect.php");

    // Delete booking
    $delstatus = delete_booking($id);

    if ($delstatus) {
        echo json_encode(array("status" => "success", "message" => "Booking deleted successfully"));
    } else {
        throw new Exception('Failed to delete booking');
    }

} catch (Exception $e) {
    echo json_encode(array("status" => "error", "message" => $e->getMessage()));
}
?>