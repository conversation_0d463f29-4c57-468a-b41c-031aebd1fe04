<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once dirname(__DIR__) . '/config/config.php';

// Start session
session_start();

// Simple password reset process
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

    // Validate input
    if (empty($username) || empty($email) || empty($new_password) || empty($confirm_password)) {
        $_SESSION['reset_error'] = 'Please fill in all fields.';
        header('Location: reset_password.php');
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['reset_error'] = 'Please enter a valid email address.';
        header('Location: reset_password.php');
        exit;
    }

    // Check if passwords match
    if ($new_password !== $confirm_password) {
        $_SESSION['reset_error'] = 'Passwords do not match.';
        header('Location: reset_password.php');
        exit;
    }

    // Check password strength
    if (strlen($new_password) < 8) {
        $_SESSION['reset_error'] = 'Password must be at least 8 characters long.';
        header('Location: reset_password.php');
        exit;
    }

    try {
        // Connect to the database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

        // Check if username and email match
        $stmt = $pdo->prepare("SELECT id, user, email FROM kp_login WHERE user = ? AND email = ? AND status = 1");
        $stmt->execute([$username, $email]);
        $user = $stmt->fetch();

        if ($user) {
            // User found, update the password
            // Hash the password using Argon2id as specified in the table comment
            $hashedPassword = password_hash($new_password, PASSWORD_ARGON2ID, PASSWORD_OPTIONS);

            // Update the password in the database
            $updateStmt = $pdo->prepare("UPDATE kp_login SET pass = ?, update_date = NOW() WHERE id = ?");
            $result = $updateStmt->execute([$hashedPassword, $user['id']]);

            if ($result) {
                $_SESSION['reset_success'] = 'Password has been reset successfully. You can now login with your new password.';
                header('Location: reset_password.php');
                exit;
            } else {
                $_SESSION['reset_error'] = 'Failed to update password. Please try again.';
                header('Location: reset_password.php');
                exit;
            }
        } else {
            // Username and email don't match or account is disabled
            $_SESSION['reset_error'] = 'Username and email do not match our records or account is disabled.';
            header('Location: reset_password.php');
            exit;
        }
    } catch (PDOException $e) {
        // Log the error but don't expose details to the user
        error_log("Database error: " . $e->getMessage());
        $_SESSION['reset_error'] = 'An error occurred while processing your request. Please try again later.';
        header('Location: reset_password.php');
        exit;
    }
} else {
    // Not a POST request
    $_SESSION['reset_error'] = 'Invalid access method. Please use the reset password form.';
    header('Location: reset_password.php');
    exit;
}
?>
