<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Load JSON Files</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .result-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">Auto-Load JSON Files</h1>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Create JSON Files</h5>
            </div>
            <div class="card-body">
                <form id="createFilesForm">
                    <div class="mb-3">
                        <label for="upToDate" class="form-label">Create files up to date:</label>
                        <input type="date" class="form-control" id="upToDate" name="upToDate" required>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">Create Files</button>
                        <button type="button" id="useCurrentDate" class="btn btn-secondary">Use Current Date</button>
                    </div>
                </form>
                
                <div class="result-container d-none" id="resultContainer">
                    <h6>Result:</h6>
                    <pre id="resultOutput"></pre>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Auto-Load Status</h5>
            </div>
            <div class="card-body">
                <p>The system will automatically create JSON files for all dates up to the current date when this page loads.</p>
                <div id="autoLoadStatus" class="alert alert-info">
                    Checking auto-load status...
                </div>
                <button id="triggerAutoLoad" class="btn btn-info">Trigger Auto-Load Now</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default date to today
            const today = new Date();
            const formattedDate = today.toISOString().split('T')[0];
            document.getElementById('upToDate').value = formattedDate;
            
            // Use current date button
            document.getElementById('useCurrentDate').addEventListener('click', function() {
                document.getElementById('upToDate').value = formattedDate;
            });
            
            // Form submission
            document.getElementById('createFilesForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const date = document.getElementById('upToDate').value;
                createFiles(date);
            });
            
            // Trigger auto-load button
            document.getElementById('triggerAutoLoad').addEventListener('click', function() {
                triggerAutoLoad();
            });
            
            // Auto-load on page load
            triggerAutoLoad();
        });
        
        function createFiles(date) {
            const resultContainer = document.getElementById('resultContainer');
            const resultOutput = document.getElementById('resultOutput');
            
            resultContainer.classList.remove('d-none');
            resultOutput.textContent = 'Loading...';
            
            fetch(`CreateFileByDate.php?upTodate=${date}`)
                .then(response => response.json())
                .then(data => {
                    resultOutput.textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    resultOutput.textContent = `Error: ${error.message}`;
                });
        }
        
        function triggerAutoLoad() {
            const statusElement = document.getElementById('autoLoadStatus');
            statusElement.className = 'alert alert-info';
            statusElement.textContent = 'Auto-load in progress...';
            
            fetch('autoload_files.php')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        statusElement.className = 'alert alert-success';
                    } else {
                        statusElement.className = 'alert alert-danger';
                    }
                    statusElement.textContent = data.message;
                })
                .catch(error => {
                    statusElement.className = 'alert alert-danger';
                    statusElement.textContent = `Error: ${error.message}`;
                });
        }
    </script>
</body>
</html>
