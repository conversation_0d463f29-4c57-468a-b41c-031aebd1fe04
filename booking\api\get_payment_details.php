<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get booking ID from query parameter
$bookingId = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;

if (!$bookingId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing booking ID']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();
    
    // Get payment details for the booking
    $stmt = $pdo->prepare("SELECT payment_status, payment_note, attactfile FROM kp_booking WHERE booking_id = ?");
    $stmt->execute([$bookingId]);
    $paymentDetails = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$paymentDetails) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    echo json_encode([
        'success' => true,
        'payment_status' => $paymentDetails['payment_status'] ?? 'WP',
        'payment_note' => $paymentDetails['payment_note'] ?? '',
        'attactfile' => $paymentDetails['attactfile'] ?? ''
    ]);
    
} catch (Exception $e) {
    error_log("Error getting payment details: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
