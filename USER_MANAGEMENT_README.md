# User Management System - <PERSON><PERSON><PERSON><PERSON> Backend

## Overview
The Sawasdee Backend now includes a comprehensive user management system with three distinct user roles: Super Administrator, Administrator, and User. This system provides role-based access control (RBAC) to ensure proper security and functionality separation.

## User Roles

### 1. Super Administrator (`super_admin`)
- **Full system access** - Can access all features and settings
- **User management** - Can create, edit, and delete all users
- **System settings** - Can modify system configurations
- **All booking operations** - Full booking management capabilities
- **Floor & table management** - Can configure restaurant layout
- **Agent management** - Can manage booking agents
- **Export data** - Can export system data
- **View reports** - Access to all system reports

### 2. Administrator (`admin`)
- **View all bookings** - Can see all reservation data
- **Edit/delete bookings** - Can modify and remove bookings
- **Floor management** - Can manage floor plans and layouts
- **Table management** - Can configure table settings
- **Agent management** - Can manage booking agents
- **Export data** - Can export booking and system data
- **View reports** - Access to system reports
- **Limited user management** - Can manage regular users only

### 3. User (`user`)
- **View bookings** - Can see booking information
- **Create bookings** - Can make new reservations
- **Edit own bookings** - Can modify their own bookings only
- **View floorplan** - Can see restaurant layout

## Setup Instructions

### 1. Database Setup
Run the following scripts in order:

```bash
# Update the login table structure
mysql -u [username] -p [database] < login/update_user_roles.sql

# Or create the table from scratch
mysql -u [username] -p [database] < login/create_login_table.sql
```

### 2. Create Default Users
Access the user creation script via web browser:
```
http://localhost/SawasdeeBackend/login/create_default_users.php
```

This will create three default users:
- **superadmin** / superadmin123
- **admin** / admin123  
- **user** / user123

### 3. Test the System
Visit the test page to verify users were created:
```
http://localhost/SawasdeeBackend/test_users.php
```

## File Structure

```
SawasdeeBackend/
├── includes/
│   └── role_check.php          # Role-based access control functions
├── login/
│   ├── create_login_table.sql  # Database table creation
│   ├── update_user_roles.sql   # Update existing table
│   ├── create_default_users.php # User creation script
│   └── login_process.php       # Updated login handler
├── manage/
│   ├── users.php              # User management interface
│   └── user_actions.php       # User CRUD operations
├── dashboard.php              # Updated dashboard with role support
└── test_users.php            # Testing interface
```

## Key Functions

### Role Checking Functions (`includes/role_check.php`)
- `isLoggedIn()` - Check if user is authenticated
- `getCurrentUserRole()` - Get current user's role
- `isSuperAdmin()` - Check if user is super admin
- `isAdmin()` - Check if user is admin or higher
- `hasPermission($feature)` - Check specific feature permissions
- `requireLogin()` - Redirect to login if not authenticated
- `requireRole($role)` - Require specific role or higher
- `canEditUser($userId, $userRole)` - Check if user can edit another user

### Usage Examples

```php
// Require login for any page
require_once 'includes/role_check.php';
requireLogin();

// Require admin access
requireRole('admin');

// Check specific permissions
if (hasPermission('user_management')) {
    // Show user management features
}

// Check if user can edit another user
if (canEditUser($targetUserId, $targetUserRole)) {
    // Show edit options
}
```

## Security Features

1. **Password Hashing**: Uses Argon2id algorithm with secure parameters
2. **Session Management**: Proper session handling with timeout
3. **CSRF Protection**: Token-based CSRF protection for forms
4. **Role Hierarchy**: Proper role inheritance (Super Admin > Admin > User)
5. **Permission Checking**: Granular permission system
6. **Audit Logging**: User actions are logged for security

## User Management Interface

### Access User Management
- Super Admins: Full access to user management
- Admins: Can manage regular users only
- Users: Cannot access user management

### Features
- **Add Users**: Create new user accounts with role assignment
- **Edit Users**: Modify user information and roles
- **Delete Users**: Remove user accounts (Super Admin only)
- **Status Management**: Enable/disable user accounts
- **Role Assignment**: Assign appropriate roles to users

## Login Process

1. User enters credentials on login page
2. System validates username and password
3. Password verification supports both legacy and hashed passwords
4. Automatic password upgrade to Argon2id if needed
5. Session variables set with user information and role
6. Redirect to appropriate dashboard based on role

## Testing

### Test Accounts
After running the setup script, you can test with:

| Username | Password | Role | Access Level |
|----------|----------|------|--------------|
| superadmin | superadmin123 | Super Admin | Full system access |
| admin | admin123 | Administrator | Limited admin access |
| user | user123 | User | Basic user access |

### Test Pages
- `test_users.php` - View all users and test credentials
- `dashboard.php` - Role-based dashboard
- `manage/users.php` - User management interface (Admin+ only)

## Important Notes

1. **Change Default Passwords**: Always change default passwords after first login
2. **Database Backup**: Backup your database before running update scripts
3. **Session Security**: Ensure proper session configuration in production
4. **HTTPS**: Use HTTPS in production for secure authentication
5. **Regular Updates**: Keep the system updated with security patches

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/config.php`
   - Ensure MySQL service is running

2. **Permission Denied**
   - Verify user role assignments
   - Check session variables are set correctly

3. **Login Issues**
   - Verify user exists and is active
   - Check password hash format

4. **Role Not Working**
   - Ensure `includes/role_check.php` is included
   - Verify session is started

### Debug Mode
Enable error reporting for debugging:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

## Future Enhancements

1. **Two-Factor Authentication**: Add 2FA support
2. **Password Policies**: Implement password complexity requirements
3. **User Groups**: Add user group management
4. **Activity Logging**: Enhanced user activity tracking
5. **API Access**: Role-based API authentication
6. **Single Sign-On**: Integration with external authentication systems

## Support

For issues or questions regarding the user management system, please check:
1. Error logs in your web server
2. Database connection settings
3. File permissions
4. Session configuration

Remember to test all functionality in a development environment before deploying to production.
