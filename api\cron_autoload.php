<?php
/**
 * <PERSON><PERSON> script to auto-load JSON files
 * This script can be run via cron job to automatically create JSON files
 * 
 * Example cron entry (daily at midnight):
 * 0 0 * * * php /path/to/cron_autoload.php
 */

// Set error reporting
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Set the path to the log file
$logFile = __DIR__ . '/autoload_log.txt';

// Function to log messages
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Log the start of the process
logMessage("Starting cron job for auto-loading JSON files");

// Get current date
$currentDate = date('Y-m-d');
logMessage("Creating files up to $currentDate");

// Include database connection
include '../dbconnect/_dbconnect.php';

// Create directory structure if it doesn't exist
$year = date('Y');
$month = date('m');
$folder = '../api/json/' . $year . '/' . $month;

if (!file_exists($folder)) {
    if (mkdir($folder, 0777, true)) {
        logMessage("Created directory: $folder");
    } else {
        logMessage("Failed to create directory: $folder");
    }
}

// Create JSON files for each day from the 1st to the current date
$day = date('d');
$successCount = 0;
$errorCount = 0;

for ($i = 1; $i <= intval($day); $i++) {
    $date = sprintf('%s-%s-%02d', $year, $month, $i);
    try {
        create_json_file_by_date($date);
        logMessage("Successfully created JSON file for $date");
        $successCount++;
    } catch (Exception $e) {
        logMessage("Error creating JSON file for $date: " . $e->getMessage());
        $errorCount++;
    }
}

// Create zone JSON file
try {
    create_json_file_by_zone($currentDate);
    logMessage("Successfully created zone JSON file");
    $successCount++;
} catch (Exception $e) {
    logMessage("Error creating zone JSON file: " . $e->getMessage());
    $errorCount++;
}

// Generate monthly summary
try {
    $json_result = generate_json_by_month($year, $month);
    logMessage("Successfully generated monthly summary");
    $successCount++;
} catch (Exception $e) {
    logMessage("Error generating monthly summary: " . $e->getMessage());
    $errorCount++;
}

// Log the completion of the process
logMessage("Cron job completed. Success: $successCount, Errors: $errorCount");
