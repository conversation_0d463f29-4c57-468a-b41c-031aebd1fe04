<?php

include '../dbconnect/_dbconnect.php';

// Check if upTodate parameter is provided
if (!isset($_GET['upTodate'])) {
    echo json_encode(['status' => 'error', 'message' => 'Missing upTodate parameter']);
    exit();
}

// Get the date parameter
$date = $_GET['upTodate']; // Format: YYYY-MM-DD (e.g., 2025-04-10)

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid date format. Use YYYY-MM-DD']);
    exit();
}

// Parse the date
$year = substr($date, 0, 4);
$month = substr($date, 5, 2);
$day = substr($date, 8, 2);

// Validate date components
if (!checkdate(intval($month), intval($day), intval($year))) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid date']);
    exit();
}

// Create directory structure if it doesn't exist
$folder = '../api/json/' . $year . '/' . $month;
if (!file_exists($folder)) {
    mkdir($folder, 0777, true);
}

// Create JSON files for each day from the 1st to the provided date
$results = [];
for ($i = 1; $i <= intval($day); $i++) {
    $currentDate = sprintf('%s-%s-%02d', $year, $month, $i);
    try {
        create_json_file_by_date($currentDate);
        $results[] = ['date' => $currentDate, 'status' => 'success'];
    } catch (Exception $e) {
        $results[] = ['date' => $currentDate, 'status' => 'error', 'message' => $e->getMessage()];
    }
}

// Create zone JSON file
try {
    create_json_file_by_zone($date);
    $results[] = ['file' => 'json_by_zone.json', 'status' => 'success'];
} catch (Exception $e) {
    $results[] = ['file' => 'json_by_zone.json', 'status' => 'error', 'message' => $e->getMessage()];
}

// Generate monthly summary
$json_result = generate_json_by_month($year, $month);

// Return results
echo json_encode([
    'status' => 'success',
    'message' => 'Files created successfully',
    'date_range' => "$year-$month-01 to $date",
    'files_created' => $results,
    'monthly_data' => json_decode($json_result)
], JSON_PRETTY_PRINT);
exit();
