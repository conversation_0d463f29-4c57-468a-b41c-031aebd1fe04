-- Create payment references table to store payment reference images and notes
DROP TABLE IF EXISTS `kp_payment_references`;

CREATE TABLE `kp_payment_references` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `booking_id` INT NOT NULL,
  `payment_status` VARCHAR(10) NOT NULL,
  `reference_image` VARCHAR(255) DEFAULT NULL,
  `note` TEXT DEFAULT NULL,
  `updated_by` INT DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `booking_id` (`booking_id`),
  <PERSON>EY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add foreign key constraint if kp_booking table exists
-- ALTER TABLE `kp_payment_references` 
-- ADD CONSTRAINT `fk_payment_ref_booking` 
-- FOREIGN KEY (`booking_id`) REFERENCES `kp_booking` (`booking_id`) ON DELETE CASCADE;
