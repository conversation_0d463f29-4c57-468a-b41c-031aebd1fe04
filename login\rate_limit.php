<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if the user has exceeded the maximum number of login attempts
 * 
 * @return bool True if the user is allowed to attempt login, false if rate limited
 */
function check_rate_limit() {
    // Initialize login attempts if not set
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = 0;
        $_SESSION['login_time'] = time();
    }
    
    // Reset attempts after 15 minutes
    if (time() - $_SESSION['login_time'] > 900) {
        $_SESSION['login_attempts'] = 0;
        $_SESSION['login_time'] = time();
    }
    
    // Check if max attempts reached (5 attempts)
    if ($_SESSION['login_attempts'] >= 5) {
        // Calculate remaining lockout time
        $remaining = 900 - (time() - $_SESSION['login_time']);
        if ($remaining > 0) {
            // User is rate limited
            return false;
        } else {
            // Reset after lockout period
            $_SESSION['login_attempts'] = 0;
            $_SESSION['login_time'] = time();
        }
    }
    
    return true;
}

/**
 * Increment the login attempts counter
 */
function increment_login_attempts() {
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = 0;
        $_SESSION['login_time'] = time();
    }
    
    $_SESSION['login_attempts']++;
}

/**
 * Reset the login attempts counter
 */
function reset_login_attempts() {
    $_SESSION['login_attempts'] = 0;
    $_SESSION['login_time'] = time();
}

/**
 * Get the remaining time until rate limit is reset
 * 
 * @return int Remaining seconds until rate limit reset
 */
function get_rate_limit_remaining_time() {
    if (!isset($_SESSION['login_time'])) {
        return 0;
    }
    
    return max(0, 900 - (time() - $_SESSION['login_time']));
}
?>
