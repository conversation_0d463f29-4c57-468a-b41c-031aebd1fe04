-- Add payment-related columns to kp_booking table if they don't exist
-- This script is safe to run multiple times
-- Check and add payment_status column
SET @exists_payment_status = 0;
SELECT COUNT(*) INTO @exists_payment_status
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'payment_status';
SET @sql_payment_status = IF(
        @exists_payment_status = 0,
        'ALTER TABLE kp_booking ADD COLUMN payment_status VARCHAR(10) DEFAULT "WP" COMMENT "Payment status: WP=Waiting Payment, Paid=Paid"',
        'SELECT "Column payment_status already exists" as message'
    );
PREPARE stmt
FROM @sql_payment_status;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
-- Check and add payment_note column
SET @exists_payment_note = 0;
SELECT COUNT(*) INTO @exists_payment_note
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'payment_note';
SET @sql_payment_note = IF(
        @exists_payment_note = 0,
        'ALTER TABLE kp_booking ADD COLUMN payment_note TEXT DEFAULT NULL COMMENT "Payment note or comment"',
        'SELECT "Column payment_note already exists" as message'
    );
PREPARE stmt
FROM @sql_payment_note;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
-- Check and add attactfile column
SET @exists_attactfile = 0;
SELECT COUNT(*) INTO @exists_attactfile
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'attactfile';
SET @sql_attactfile = IF(
        @exists_attactfile = 0,
        'ALTER TABLE kp_booking ADD COLUMN attactfile VARCHAR(255) DEFAULT NULL COMMENT "Attachment file path"',
        'SELECT "Column attactfile already exists" as message'
    );
PREPARE stmt
FROM @sql_attactfile;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
-- Update existing records to have default payment status if they are NULL
UPDATE kp_booking
SET payment_status = 'WP'
WHERE payment_status IS NULL
    OR payment_status = '';
-- Show the results
SELECT CASE
        WHEN @exists_payment_status = 0 THEN 'payment_status column added'
        ELSE 'payment_status column exists'
    END as payment_status_result,
    CASE
        WHEN @exists_payment_note = 0 THEN 'payment_note column added'
        ELSE 'payment_note column exists'
    END as payment_note_result,
    CASE
        WHEN @exists_attactfile = 0 THEN 'attactfile column added'
        ELSE 'attactfile column exists'
    END as attactfile_result;