<?php
/**
 * Booking Controller
 *
 * Handles booking API endpoints
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/models/Booking.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';

class BookingController {
    private $booking;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            $this->booking = new Booking();

            // Start session
            Session::start();

            // Set security headers
            Security::setSecurityHeaders();

            // Check if user is logged in
            if (!Session::isLoggedIn()) {
                $this->sendJsonResponse(['error' => 'Unauthorized access'], 401);
                exit;
            }
        } catch (Exception $e) {
            // Log the error
            error_log('BookingController initialization error: ' . $e->getMessage());

            // Return a generic error to avoid exposing sensitive information
            $this->sendJsonResponse(['error' => 'System error. Please try again later.'], 500);
            exit;
        }
    }

    /**
     * Get bookings by date
     */
    public function getBookingsByDate() {
        try {

            // Validate and sanitize inputs
            $date = isset($_GET['date']) ? Security::sanitizeInput($_GET['date']) : '';
            $zoneId = isset($_GET['zone_id']) ? (int)$_GET['zone_id'] : null;

            $dateParts = explode('-', $date);
            // print_r($dateParts);
            // count dateParts[0] is 4 digi or 2 digi
            if (strlen($dateParts[0]) === 4) {
                $formattedDate = $dateParts[0] . '-' . $dateParts[1] . '-' . $dateParts[2];
            } else {
                $formattedDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
            }

            // Get bookings
            $bookings = $this->booking->getBookingsByDate($formattedDate, $zoneId);

            // Format bookings for response
            $formattedBookings = [];

            if (is_array($bookings)) {
                foreach ($bookings as $row) {
                    $booking = [
                        'Order No' => $row['orderNo'] ?? '',
                        'BookID' => $row['booking_id'] ?? 0,
                        'ZoneID' => $row['zone_id'] ?? 0,
                        'Use Date' => $row['use_date'] ?? '',
                        'Amount' => $row['amount'] ?? 0,
                        'Payment' => $row['payment_status'] ?? '',
                        'PayType' => $row['pay_type'] ?? '',
                        'Create Date' => $row['create_date'] ?? '',
                        'Customer' => $row['name'] ?? '',
                        'Phone' => $row['phone'] ?? '',
                        'Guests' => [
                            'Adults' => $row['adult'] ?? 0,
                            'Children' => $row['child'] ?? 0,
                            'Infants' => $row['infant'] ?? 0
                        ],
                        'Guide' => $row['guide'] ?? 0,
                        'FOC' => $row['inspection'] ?? 0,
                        'TL' => $row['team_leader'] ?? 0,
                        'Voucher No' => $row['voucher'] ?? '',
                        'Agent' => $row['agent'] ?? '',
                        'Request' => $row['remark'] ?? '',
                        'Special' => $row['special_request'] ?? 0,
                        'Floor' => $row['use_zone'] ?? '',
                        'Table' => $row['tables'] ?? ''
                    ];

                    $formattedBookings[] = $booking;
                }
            }

            $this->sendJsonResponse($formattedBookings);
        } catch (Exception $e) {
            // Log the error
            error_log('Error in getBookingsByDate: ' . $e->getMessage());

            // Return a generic error message
            $this->sendJsonResponse(['error' => 'Failed to fetch bookings. Please try again.'], 500);
        }
    }

    /**
     * Add a new booking
     */
    public function addBooking() {
        try {
            // Check if the request is a POST request
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendJsonResponse(['error' => 'Invalid request method'], 405);
                return;
            }

            // Validate and sanitize inputs
            $name = isset($_POST['name']) ? Security::sanitizeInput($_POST['name']) : '';
            $phone = isset($_POST['phone']) ? Security::sanitizeInput($_POST['phone']) : '';

            // Process adult count
            $adult = isset($_POST['adult']) ? Security::sanitizeInput($_POST['adult']) : 'Adult';
            if ($adult == 'Adult') { $adult = 0; } else { $adult = (int)$adult; }

            // Process child count
            $child = isset($_POST['child']) ? Security::sanitizeInput($_POST['child']) : 'Child';
            if ($child == 'Child') { $child = 0; } else { $child = (int)$child; }

            // Process infant count
            $infant = isset($_POST['infant']) ? Security::sanitizeInput($_POST['infant']) : 'Infant';
            if ($infant == 'Infant') { $infant = 0; } else { $infant = (int)$infant; }

            // Process guide count
            $guide = isset($_POST['guide']) ? Security::sanitizeInput($_POST['guide']) : 'Guide';
            if ($guide == 'Guide') { $guide = 0; } else { $guide = (int)$guide; }

            // Process FOC count
            $foc = isset($_POST['foc']) ? Security::sanitizeInput($_POST['foc']) : 'FOC';
            if ($foc == 'FOC') { $foc = 0; } else { $foc = (int)$foc; }

            // Process team leader count
            $tl = isset($_POST['tl']) ? Security::sanitizeInput($_POST['tl']) : 'T/L';
            if ($tl == 'T/L') { $tl = 0; } else { $tl = (int)$tl; }

            // Process voucher
            $voucher = isset($_POST['voucher']) ? Security::sanitizeInput($_POST['voucher']) : 'Voucher';
            if ($voucher == 'Voucher') { $voucher = ''; }

            // Process agent
            $agent = isset($_POST['agent']) ? Security::sanitizeInput($_POST['agent']) : 'Agent';
            if ($agent == 'Agent') { $agent = ''; }

            // Process remark
            $remark = isset($_POST['remark']) ? Security::sanitizeInput($_POST['remark']) : 'Remark';
            if ($remark == 'Remark') { $remark = ''; }

            // Process special request
            $specialRequest = isset($_POST['special_request']) ? (int)$_POST['special_request'] : 0;

            // Process date and zone
            $useDate = isset($_POST['useDate']) ? Security::sanitizeInput($_POST['useDate']) : '';
            $useZone = isset($_POST['useZone']) ? Security::sanitizeInput($_POST['useZone']) : 'Zone';
            if ($useZone == 'Zone') { $useZone = 0; }

            // Process floor and table
            $floor = isset($_POST['floor']) ? Security::sanitizeInput($_POST['floor']) : '';
            $table = isset($_POST['tables']) ? Security::sanitizeInput($_POST['tables']) : '';

            // Validate required fields
            if (empty($name) || empty($useDate) || empty($useZone)) {
                $this->sendJsonResponse(['error' => 'Missing required fields'], 400);
                return;
            }

            // Validate date format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $useDate)) {
                $this->sendJsonResponse(['error' => 'Invalid date format'], 400);
                return;
            }

            $cruiseId = 1;  // Hardcoded for now
            $zoneId = $useZone;
            // $userKey = Session::get('userKey');
            $userKey = Session::get('id');
            $amount = isset($_POST['amount']) ? (int)$_POST['amount'] : 0;
            $paymentType = isset($_POST['paymentType']) ? Security::sanitizeInput($_POST['paymentType']) : 'Transfer';

            // Prepare booking data
            $bookingData = [
                'name' => $name,
                'phone' => $phone,
                'adult' => $adult,
                'child' => $child,
                'infant' => $infant,
                'guide' => $guide,
                'inspection' => $foc,
                'team_leader' => $tl,
                'use_date' => $useDate,
                'use_zone' => $floor,
                'voucher' => $voucher,
                'agent' => $agent,
                'remark' => $remark,
                'cruise_id' => $cruiseId,
                'zone_id' => $zoneId,
                'tables' => $table,
                'amount' => $amount,
                'user_key' => $userKey,
                'pay_type' => $paymentType,
                'special_request' => $specialRequest,
                'payment_status' => 'WP',
                'create_date' => date('Y-m-d H:i:s'),
                'update_date' => date('Y-m-d H:i:s'),
                'create_by' => $userKey,
                'update_by' => $userKey
            ];

            // Insert booking
            $orderNo = $this->booking->createBooking($bookingData);

            if ($orderNo) {
                $this->sendJsonResponse(['status' => 'success', 'orderNo' => $orderNo]);
            } else {
                $this->sendJsonResponse(['error' => 'Failed to create booking'], 500);
            }
        } catch (Exception $e) {
            $this->sendJsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Edit a booking
     */
    public function editBooking() {
        try {
            // Check if the request is a POST request
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendJsonResponse(['error' => 'Invalid request method'], 405);
                return;
            }

            // Validate and sanitize inputs
            $bookingId = isset($_POST['booking_id']) ? (int)$_POST['booking_id'] : 0;
            $name = isset($_POST['name']) ? Security::sanitizeInput($_POST['name']) : '';
            $phone = isset($_POST['phone']) ? Security::sanitizeInput($_POST['phone']) : '';
            $adult = isset($_POST['adult']) ? (int)$_POST['adult'] : 0;
            $child = isset($_POST['child']) ? (int)$_POST['child'] : 0;
            $infant = isset($_POST['infant']) ? (int)$_POST['infant'] : 0;
            $guide = isset($_POST['guide']) ? (int)$_POST['guide'] : 0;
            $foc = isset($_POST['foc']) ? (int)$_POST['foc'] : 0;
            $tl = isset($_POST['tl']) ? (int)$_POST['tl'] : 0;
            $useDate = isset($_POST['useDate']) ? Security::sanitizeInput($_POST['useDate']) : '';
            $useZone = isset($_POST['useZone']) ? (int)$_POST['useZone'] : 0;
            $voucher = isset($_POST['voucher']) ? Security::sanitizeInput($_POST['voucher']) : '';
            $agent = isset($_POST['agent']) ? Security::sanitizeInput($_POST['agent']) : '';
            $remark = isset($_POST['remark']) ? Security::sanitizeInput($_POST['remark']) : '';
            $amount = isset($_POST['amount']) ? (float)$_POST['amount'] : 0;
            $paymentType = isset($_POST['paymentType']) ? Security::sanitizeInput($_POST['paymentType']) : 'Transfer';
            $floor = isset($_POST['floor']) ? Security::sanitizeInput($_POST['floor']) : '';
            $table = isset($_POST['tables']) ? Security::sanitizeInput($_POST['tables']) : '';

            // Validate required fields
            if ($bookingId <= 0 || empty($name) || empty($useDate) || empty($useZone)) {
                $this->sendJsonResponse(['error' => 'Missing required fields'], 400);
                return;
            }

            // Validate date format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $useDate)) {
                $this->sendJsonResponse(['error' => 'Invalid date format'], 400);
                return;
            }

            // Process special request
            $specialRequest = isset($_POST['special_request']) ? (int)$_POST['special_request'] : 0;

            // Prepare booking data
            $bookingData = [
                'name' => $name,
                'phone' => $phone,
                'adult' => $adult,
                'child' => $child,
                'infant' => $infant,
                'guide' => $guide,
                'inspection' => $foc,
                'team_leader' => $tl,
                'use_date' => $useDate,
                'zone_id' => $useZone,
                'voucher' => $voucher,
                'agent' => $agent,
                'remark' => $remark,
                'amount' => $amount,
                'use_zone' => $floor,
                'tables' => $table,
                'pay_type' => $paymentType,
                'special_request' => $specialRequest,
                'update_date' => date('Y-m-d H:i:s'),
                'update_by' => $userKey
            ];

            // Update booking
            $result = $this->booking->updateBooking($bookingId, $bookingData);

            if ($result) {
                $this->sendJsonResponse(['status' => 'success']);
            } else {
                $this->sendJsonResponse(['error' => 'Failed to update booking'], 500);
            }
        } catch (Exception $e) {
            $this->sendJsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Delete a booking
     */
    public function deleteBooking() {
        try {
            // Check if the request is a POST request
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->sendJsonResponse(['error' => 'Invalid request method'], 405);
                return;
            }

            // Validate and sanitize inputs
            $bookingId = isset($_POST['booking_id']) ? (int)$_POST['booking_id'] : 0;

            // Validate required fields
            if ($bookingId <= 0) {
                $this->sendJsonResponse(['error' => 'Missing booking ID'], 400);
                return;
            }

            // Delete booking
            $result = $this->booking->deleteBooking($bookingId);

            if ($result) {
                $this->sendJsonResponse(['status' => 'success']);
            } else {
                $this->sendJsonResponse(['error' => 'Failed to delete booking'], 500);
            }
        } catch (Exception $e) {
            $this->sendJsonResponse(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get zones
     */
    public function getZones() {
        try {
            // Get cruise ID from session or default to 1
            $cruiseId = Session::has('cruiseId') ? Session::get('cruiseId') : 1;

            // Get zones
            $zones = $this->booking->getZones($cruiseId);

            // Ensure we have an array to return
            if (!is_array($zones)) {
                $zones = [];
            }

            $this->sendJsonResponse($zones);
        } catch (Exception $e) {
            // Log the error
            error_log('Error in getZones: ' . $e->getMessage());

            // Return a generic error message
            $this->sendJsonResponse(['error' => 'Failed to fetch zones. Please try again.'], 500);
        }
    }

    /**
     * Get booked tables
     */
    public function getBookedTables() {
        try {
            // Validate and sanitize inputs
            $date = isset($_GET['date']) ? Security::sanitizeInput($_GET['date']) : '';

            // Validate date format (DD-MM-YYYY)
            if (!preg_match('/^\d{2}-\d{2}-\d{4}$/', $date)) {
                $this->sendJsonResponse(['error' => 'Invalid date format'], 400);
                return;
            }

            // Convert date format from DD-MM-YYYY to YYYY-MM-DD
            $dateParts = explode('-', $date);
            $formattedDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];

            // Get booked tables
            $bookedTables = $this->booking->getBookedTables($formattedDate);
            if (!is_array($bookedTables)) {
                $bookedTables = [];
            }

            // Get disabled tables
            $disabledTables = $this->booking->getDisabledTables($formattedDate);
            if (!is_array($disabledTables)) {
                $disabledTables = [];
            }

            $this->sendJsonResponse([
                'booked_tables' => $bookedTables,
                'disabled_tables' => $disabledTables
            ]);
        } catch (Exception $e) {
            // Log the error
            error_log('Error in getBookedTables: ' . $e->getMessage());

            // Return a generic error message
            $this->sendJsonResponse(['error' => 'Failed to fetch table data. Please try again.'], 500);
        }
    }

    /**
     * Send JSON response
     *
     * @param mixed $data Response data
     * @param int $statusCode HTTP status code
     */
    private function sendJsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);

        // Set CORS headers to allow requests from the same origin
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');

        // Set content type
        header('Content-Type: application/json');

        // Handle OPTIONS preflight requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit(0);
        }

        // Output JSON data
        echo json_encode($data);
    }
}
