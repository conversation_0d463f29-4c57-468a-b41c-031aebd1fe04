-- Drop the table if it exists
DROP TABLE IF EXISTS `kp_booking`;
-- Create the booking table
CREATE TABLE `kp_booking` (
  `booking_id` INT NOT NULL AUTO_INCREMENT,
  `orderNo` VARCHAR(20) NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `phone` VARCHAR(20) DEFAULT NULL,
  `adult` INT DEFAULT 0,
  `child` INT DEFAULT 0,
  `infant` INT DEFAULT 0,
  `guide` INT DEFAULT 0,
  `inspection` INT DEFAULT 0,
  `team_leader` INT DEFAULT 0,
  `use_date` DATE NOT NULL,
  `use_zone` VARCHAR(10) DEFAULT NULL COMMENT 'Floor',
  `voucher` VARCHAR(50) DEFAULT NULL,
  `agent` VARCHAR(100) DEFAULT NULL,
  `remark` TEXT DEFAULT NULL,
  `special_request` INT DEFAULT 0 COMMENT '0=None, 1=Birthday, 2=Anniversary',
  `cruise_id` INT DEFAULT 1,
  `zone_id` INT NOT NULL,
  `tables` VARCHAR(255) DEFAULT NULL COMMENT 'Table IDs',
  `amount` DECIMAL(10, 2) DEFAULT 0.00,
  `user_key` VARCHAR(50) DEFAULT NULL,
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`booking_id`),
  UNIQUE KEY `orderNo` (`orderNo`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;