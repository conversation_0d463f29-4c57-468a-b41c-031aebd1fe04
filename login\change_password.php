<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: index.php');
    exit;
}

// Check if user actually needs to change password
if (!isset($_SESSION['first_login']) || $_SESSION['first_login'] != 1) {
    // User doesn't need to change password, redirect to dashboard
    header('Location: ../floorplan/');
    exit;
}

// Include database connection
require_once dirname(__DIR__) . '/dbconnect/_dbconnect.php';

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error_message = 'Please fill in all fields.';
    } elseif ($new_password !== $confirm_password) {
        $error_message = 'New passwords do not match.';
    } elseif (strlen($new_password) < 8) {
        $error_message = 'New password must be at least 8 characters long.';
    } elseif ($current_password !== 'pwd123') {
        $error_message = 'Current password is incorrect.';
    } else {
        try {
            // Connect to the database
            $pdo = db_connect();
            
            // Hash the new password
            $hashedPassword = password_hash($new_password, PASSWORD_ARGON2ID, [
                'memory_cost' => 65536,
                'time_cost' => 4,
                'threads' => 3,
            ]);
            
            // Update the password and set first_login to 0
            $stmt = $pdo->prepare("UPDATE kp_login SET pass = ?, first_login = 0 WHERE id = ?");
            $result = $stmt->execute([$hashedPassword, $_SESSION['id']]);
            
            if ($result) {
                // Update session to remove first_login flag
                unset($_SESSION['first_login']);
                
                $success_message = 'Password changed successfully! Redirecting to dashboard...';
                
                // Redirect after 2 seconds
                header("refresh:2;url=../floorplan/");
            } else {
                $error_message = 'Failed to update password. Please try again.';
            }
            
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            $error_message = 'An error occurred while updating your password. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicon icon-->
    <link rel="shortcut icon" type="image/png" href="../assets/images/logos/favicon.png" />

    <!-- Core Css -->
    <link rel="stylesheet" href="../assets/css/styles.css" />

    <title>Change Password - Sawasdee Admin</title>
    <style>
        .password-requirements {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .password-requirements ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        .password-requirements li {
            margin-bottom: 0.25rem;
        }
        .requirement-met {
            color: #28a745;
        }
        .requirement-not-met {
            color: #dc3545;
        }
    </style>
</head>

<body>
    <!-- Preloader -->
    <div class="preloader">
        <img src="../assets/images/logos/favicon.png" alt="loader" class="lds-ripple img-fluid" />
    </div>

    <div id="main-wrapper">
        <div class="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
            <div class="position-relative z-index-5">
                <div class="row">
                    <div class="col-xl-3 col-xxl-3">
                    </div>
                    <div class="col-xl-6 col-xxl-6">
                        <div class="authentication-login min-vh-100 bg-body row justify-content-center align-items-center p-4">
                            <div class="col-sm-8 col-md-6 col-xl-9">
                                <h2 class="mb-3 fs-7 fw-bolder">Change Password Required</h2>
                                <p class="mb-4">This is your first login. Please change your password to continue.</p>
                                
                                <?php if ($error_message): ?>
                                <div class="alert alert-danger" role="alert">
                                    <i class="ti ti-alert-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($success_message): ?>
                                <div class="alert alert-success" role="alert">
                                    <i class="ti ti-check me-2"></i><?php echo htmlspecialchars($success_message); ?>
                                </div>
                                <?php endif; ?>

                                <form method="post" action="">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <div class="form-text">Enter your default password: pwd123</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                    
                                    <div class="password-requirements mb-4">
                                        <strong>Password Requirements:</strong>
                                        <ul>
                                            <li id="length-req">At least 8 characters long</li>
                                            <li id="match-req">Passwords must match</li>
                                        </ul>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100 py-8 mb-4 rounded-2">Change Password</button>
                                </form>
                                
                                <div class="d-flex align-items-center">
                                    <p class="fs-4 mb-0 text-dark">Logged in as: <strong><?php echo htmlspecialchars($_SESSION['username']); ?></strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-xxl-3">
                    </div>
                </div>
            </div>
        </div>
    </div>

  <!-- Import Js Files -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> <!-- Link to jQuery -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
    <script>
        // Password validation
        $(document).ready(function() {
            function validatePassword() {
                const newPassword = $('#new_password').val();
                const confirmPassword = $('#confirm_password').val();
                
                // Check length requirement
                if (newPassword.length >= 8) {
                    $('#length-req').removeClass('requirement-not-met').addClass('requirement-met');
                } else {
                    $('#length-req').removeClass('requirement-met').addClass('requirement-not-met');
                }
                
                // Check password match
                if (newPassword && confirmPassword && newPassword === confirmPassword) {
                    $('#match-req').removeClass('requirement-not-met').addClass('requirement-met');
                } else if (confirmPassword) {
                    $('#match-req').removeClass('requirement-met').addClass('requirement-not-met');
                }
            }
            
            $('#new_password, #confirm_password').on('keyup', validatePassword);
        });
    </script>
</body>

</html>
