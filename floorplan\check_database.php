<?php
// Simple script to check if floor booking data is in the database
include '../dbconnect/_dbconnect.php';

echo "<h2>Floor Booking Database Check</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
    th { background-color: #f2f2f2; font-weight: bold; }
    .success { background-color: #d4edda; }
    .info { color: blue; }
    .highlight { background-color: #fff3cd; }
    .refresh-btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; display: inline-block; }
    .count { font-size: 18px; font-weight: bold; margin: 10px 0; }
</style>";

// Add refresh button
echo "<a href='check_database.php' class='refresh-btn'>🔄 Refresh Data</a>";
echo "<p><strong>Last Updated:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    $conn = db_connect();

    // Get total booking count
    $totalSql = "SELECT COUNT(*) as total FROM kp_booking";
    $totalStmt = $conn->prepare($totalSql);
    $totalStmt->execute();
    $totalCount = $totalStmt->fetch()['total'];

    // Get today's booking count
    $today = date('Y-m-d');
    $todaySql = "SELECT COUNT(*) as today_total FROM kp_booking WHERE DATE(create_date) = :today";
    $todayStmt = $conn->prepare($todaySql);
    $todayStmt->bindParam(':today', $today);
    $todayStmt->execute();
    $todayCount = $todayStmt->fetch()['today_total'];

    echo "<div class='count'>📊 Total Bookings: {$totalCount} | Today's Bookings: {$todayCount}</div>";

    // Check recent bookings (last 7 days)
    $sql = "SELECT * FROM kp_booking WHERE create_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) ORDER BY create_date DESC LIMIT 15";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $bookings = $stmt->fetchAll();
    
    echo "<h3 class='info'>📋 Recent Bookings (Last 7 Days)</h3>";

    if (count($bookings) > 0) {
        echo "<table>";
        echo "<tr>
                <th>Order No</th>
                <th>Customer</th>
                <th>Phone</th>
                <th>Use Date</th>
                <th>Floor</th>
                <th>Tables</th>
                <th>Amount</th>
                <th>Agent</th>
                <th>Voucher</th>
                <th>Remark</th>
                <th>Create Date</th>
                <th>Status</th>
              </tr>";

        $floorBookingCount = 0;
        foreach ($bookings as $booking) {
            $tablesCount = !empty($booking['tables']) ? count(explode(',', $booking['tables'])) : 0;
            $isFloorBooking = $tablesCount > 50; // Floor bookings have 120 tables
            $rowClass = $isFloorBooking ? 'success' : '';
            if ($isFloorBooking) $floorBookingCount++;

            // Check if this is a recent booking (within last hour)
            $isRecent = (strtotime($booking['create_date']) > (time() - 3600));
            if ($isRecent) $rowClass .= ' highlight';

            echo "<tr class='{$rowClass}'>";
            echo "<td><strong>" . htmlspecialchars($booking['orderNo']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($booking['name']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['use_date']) . "</td>";
            echo "<td>Floor " . htmlspecialchars($booking['use_zone']) . "</td>";
            echo "<td>" . $tablesCount . ($isFloorBooking ? ' <strong>(ENTIRE FLOOR)</strong>' : '') . "</td>";
            echo "<td>฿" . number_format($booking['amount']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['agent']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['voucher']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['remark']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['create_date']) . ($isRecent ? ' <span style="color:red;">🔥 NEW</span>' : '') . "</td>";
            echo "<td>" . htmlspecialchars($booking['payment_status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";

        echo "<div class='count'>🏢 Floor Bookings in Recent Data: {$floorBookingCount}</div>";
        
        // Count floor bookings
        $floorBookings = array_filter($bookings, function($booking) {
            $tablesCount = !empty($booking['tables']) ? count(explode(',', $booking['tables'])) : 0;
            return $tablesCount > 50;
        });
        
        echo "<p class='success'><strong>Floor Bookings Found: " . count($floorBookings) . "</strong></p>";
        
    } else {
        echo "<p>No recent bookings found.</p>";
    }
    
    // Check today's bookings specifically
    echo "<h3 class='info'>Today's Bookings</h3>";
    $today = date('Y-m-d');
    $sql = "SELECT * FROM kp_booking WHERE use_date = :today ORDER BY create_date DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':today', $today);
    $stmt->execute();
    $todayBookings = $stmt->fetchAll();
    
    if (count($todayBookings) > 0) {
        echo "<table>";
        echo "<tr>
                <th>Order No</th>
                <th>Customer</th>
                <th>Phone</th>
                <th>Floor</th>
                <th>Tables Count</th>
                <th>Amount</th>
                <th>Remark</th>
              </tr>";
        
        foreach ($todayBookings as $booking) {
            $tablesCount = !empty($booking['tables']) ? count(explode(',', $booking['tables'])) : 0;
            $isFloorBooking = $tablesCount > 50;
            $rowClass = $isFloorBooking ? 'success' : '';
            
            echo "<tr class='{$rowClass}'>";
            echo "<td>" . htmlspecialchars($booking['orderNo']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['name']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['use_zone']) . "</td>";
            echo "<td>" . $tablesCount . ($isFloorBooking ? ' (ENTIRE FLOOR)' : '') . "</td>";
            echo "<td>" . number_format($booking['amount']) . "</td>";
            echo "<td>" . htmlspecialchars($booking['remark']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No bookings for today ({$today}).</p>";
    }
    
    // Show database connection info
    echo "<h3 class='info'>Database Connection Info</h3>";
    echo "<p>Connected to database: <strong>kp_service</strong></p>";
    echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
