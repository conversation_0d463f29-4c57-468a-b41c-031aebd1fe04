<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Table Reservation Marking System - Demo</title>
    <link href="../assets/libs/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/floorplan.css" rel="stylesheet">
    <link href="css/table-styles.css" rel="stylesheet">
    <style>
        .demo-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .status-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }
        .demo-table {
            display: inline-block;
            margin: 5px;
            cursor: pointer;
        }
        .demo-table rect {
            transition: all 0.3s ease;
        }
        .demo-controls {
            margin-bottom: 20px;
        }
        .demo-controls button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="mb-4">Enhanced Table Reservation Marking System</h1>
        
        <!-- Status Legend -->
        <div class="demo-section">
            <h3>Reservation Status Legend</h3>
            <div class="status-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #539bff;"></div>
                    <span>Available</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #00FF00;"></div>
                    <span>Selected</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #cccccc;"></div>
                    <span>Booked</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4caf50;"></div>
                    <span>Confirmed</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9800; animation: pulse-pending 2s infinite;"></div>
                    <span>Pending</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #f44336; border-style: dashed;"></div>
                    <span>Cancelled</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #999999; opacity: 0.7;"></div>
                    <span>Disabled</span>
                </div>
            </div>
        </div>

        <!-- Demo Tables -->
        <div class="demo-section">
            <h3>Interactive Demo Tables</h3>
            <div class="demo-controls">
                <button class="btn btn-primary" onclick="setTableStatus('demo-table-1', 'available')">Set Available</button>
                <button class="btn btn-success" onclick="setTableStatus('demo-table-1', 'selected')">Set Selected</button>
                <button class="btn btn-secondary" onclick="setTableStatus('demo-table-1', 'booked')">Set Booked</button>
                <button class="btn btn-info" onclick="setTableStatus('demo-table-1', 'confirmed')">Set Confirmed</button>
                <button class="btn btn-warning" onclick="setTableStatus('demo-table-1', 'pending')">Set Pending</button>
                <button class="btn btn-danger" onclick="setTableStatus('demo-table-1', 'cancelled')">Set Cancelled</button>
                <button class="btn btn-dark" onclick="setTableStatus('demo-table-1', 'disabled')">Set Disabled</button>
            </div>
            
            <div class="demo-tables">
                <!-- Demo Table 1 -->
                <div class="demo-table">
                    <svg width="80" height="50" viewBox="0 0 80 50">
                        <a href="javascript:;" id="box-demo-table-1" class="svg-box" data-value="0">
                            <rect id="rbox-demo-table-1" x="5" y="5" width="70" height="40" fill="#539bff" stroke="#ffffff" stroke-width="2" rx="5" ry="5" />
                            <text x="40" y="28" font-size="12" fill="#ffffff" text-anchor="middle" font-weight="bold">A1</text>
                        </a>
                    </svg>
                </div>

                <!-- Demo Table 2 -->
                <div class="demo-table">
                    <svg width="80" height="50" viewBox="0 0 80 50">
                        <a href="javascript:;" id="box-demo-table-2" class="svg-box booked-disabled" data-value="0">
                            <rect id="rbox-demo-table-2" x="5" y="5" width="70" height="40" fill="#cccccc" stroke="#ffffff" stroke-width="2" rx="5" ry="5" />
                            <text x="40" y="28" font-size="12" fill="#ffffff" text-anchor="middle" font-weight="bold">A2</text>
                            <div class="reservation-label">BOOKED</div>
                        </a>
                    </svg>
                </div>

                <!-- Demo Table 3 -->
                <div class="demo-table">
                    <svg width="80" height="50" viewBox="0 0 80 50">
                        <a href="javascript:;" id="box-demo-table-3" class="svg-box pending-reservation" data-value="0">
                            <rect id="rbox-demo-table-3" x="5" y="5" width="70" height="40" fill="#ff9800" stroke="#f57c00" stroke-width="2" stroke-dasharray="3, 3" rx="5" ry="5" />
                            <text x="40" y="28" font-size="12" fill="#ffffff" text-anchor="middle" font-weight="bold">A3</text>
                            <div class="reservation-label">PENDING</div>
                        </a>
                    </svg>
                </div>

                <!-- Demo Table 4 -->
                <div class="demo-table">
                    <svg width="80" height="50" viewBox="0 0 80 50">
                        <a href="javascript:;" id="box-demo-table-4" class="svg-box confirmed-reservation" data-value="0">
                            <rect id="rbox-demo-table-4" x="5" y="5" width="70" height="40" fill="#4caf50" stroke="#388e3c" stroke-width="2" rx="5" ry="5" />
                            <text x="40" y="28" font-size="12" fill="#ffffff" text-anchor="middle" font-weight="bold">A4</text>
                            <div class="reservation-label">CONFIRMED</div>
                        </a>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="demo-section">
            <h3>Enhanced Features</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Visual Enhancements</h5>
                    <ul>
                        <li>Color-coded reservation statuses</li>
                        <li>Animated pending reservations (pulsing effect)</li>
                        <li>Dashed borders for cancelled reservations</li>
                        <li>Status change animations</li>
                        <li>New booking flash animations</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Functional Improvements</h5>
                    <ul>
                        <li>Enhanced tooltip information</li>
                        <li>Customer name and booking time display</li>
                        <li>Real-time status updates</li>
                        <li>Reservation statistics tracking</li>
                        <li>Backward compatibility with existing system</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- API Integration -->
        <div class="demo-section">
            <h3>API Integration</h3>
            <p>The enhanced system includes:</p>
            <ul>
                <li><strong>get_enhanced_reservations.php</strong> - Returns detailed reservation data with status information</li>
                <li><strong>reservation-manager.js</strong> - Handles advanced reservation marking and status management</li>
                <li><strong>Enhanced CSS</strong> - Provides visual styling for all reservation states</li>
                <li><strong>Backward Compatibility</strong> - Falls back to original system if enhanced features are unavailable</li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Demo function to change table status
        function setTableStatus(tableId, status) {
            const $box = $(`#box-${tableId}`);
            const $rect = $(`#rbox-${tableId}`);
            
            // Remove all status classes
            $box.removeClass('booked-disabled pending-reservation confirmed-reservation cancelled-reservation table-disabled');
            $box.find('.reservation-label').remove();
            
            // Apply new status
            switch (status) {
                case 'available':
                    $rect.attr('fill', '#539bff');
                    $rect.attr('stroke', '#ffffff');
                    $rect.attr('stroke-width', '2');
                    $rect.removeAttr('stroke-dasharray');
                    break;
                case 'selected':
                    $rect.attr('fill', '#00FF00');
                    $box.attr('data-value', '1');
                    break;
                case 'booked':
                    $box.addClass('booked-disabled');
                    $rect.attr('fill', '#cccccc');
                    $box.append('<div class="reservation-label">BOOKED</div>');
                    break;
                case 'confirmed':
                    $box.addClass('confirmed-reservation');
                    $rect.attr('fill', '#4caf50');
                    $rect.attr('stroke', '#388e3c');
                    $box.append('<div class="reservation-label">CONFIRMED</div>');
                    break;
                case 'pending':
                    $box.addClass('pending-reservation');
                    $rect.attr('fill', '#ff9800');
                    $rect.attr('stroke', '#f57c00');
                    $rect.attr('stroke-dasharray', '3, 3');
                    $box.append('<div class="reservation-label">PENDING</div>');
                    break;
                case 'cancelled':
                    $box.addClass('cancelled-reservation');
                    $rect.attr('fill', '#f44336');
                    $rect.attr('stroke', '#d32f2f');
                    $rect.attr('stroke-dasharray', '5, 5');
                    $box.append('<div class="reservation-label">CANCELLED</div>');
                    break;
                case 'disabled':
                    $box.addClass('table-disabled');
                    $rect.attr('fill', '#999999');
                    $box.append('<div class="reservation-label">DISABLED</div>');
                    break;
            }
            
            // Add animation
            $box.addClass('status-change-animation');
            setTimeout(() => {
                $box.removeClass('status-change-animation');
            }, 1500);
        }
    </script>
</body>
</html>
