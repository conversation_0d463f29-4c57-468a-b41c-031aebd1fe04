/**
 * Enhanced Reservation Manager
 * Handles advanced reservation marking, status management, and real-time updates
 */

window.ReservationManager = {
    
    /**
     * Load enhanced reservations with detailed status information
     * @param {string} date - The date in YYYY-MM-DD format
     * @param {string} zone - The zone/floor number
     * @param {boolean} includeStats - Whether to include statistics
     */
    loadEnhancedReservations: function(date, zone, includeStats = false) {
        try {
            if (!window.updatingAllFloors) {
                window.showLoadingOverlay();
            }

            const formattedDate = this.formatDate(date);
            const safeZone = this.validateZone(zone);

            console.log('Loading enhanced reservations for date:', formattedDate, 'zone:', safeZone);

            const params = { 
                useDate: formattedDate, 
                useZone: safeZone 
            };
            
            if (includeStats) {
                params.statistics = 'true';
            }

            $.ajax({
                url: 'get_enhanced_reservations.php',
                type: 'GET',
                data: params,
                dataType: 'json',
                success: this.handleEnhancedReservationsSuccess.bind(this, safeZone),
                error: this.handleEnhancedReservationsError.bind(this)
            });
        } catch (e) {
            console.error('Error in loadEnhancedReservations:', e);
            this.hideLoadingOverlayIfNeeded();
        }
    },

    /**
     * Handle successful enhanced reservations response
     */
    handleEnhancedReservationsSuccess: function(safeZone, response) {
        try {
            let reservations = [];
            let statistics = null;

            // Handle different response formats
            if (Array.isArray(response)) {
                reservations = response;
            } else if (response.reservations) {
                reservations = response.reservations;
                statistics = response.statistics;
            }

            console.log('Loaded', reservations.length, 'enhanced reservations for zone', safeZone);

            // Mark reservations with enhanced status support
            if (window.markTableReservations) {
                window.markTableReservations(reservations, safeZone);
            }

            // Load disabled tables
            if (window.DataLoader && window.DataLoader.loadDisabledTables) {
                window.DataLoader.loadDisabledTables(safeZone);
            }

            // Update floor occupancy
            if (!window.updatingAllFloors) {
                if (window.FloorOccupancy && window.FloorOccupancy.updateFloorOccupancy) {
                    window.FloorOccupancy.updateFloorOccupancy(safeZone, reservations.length);
                }
                window.hideLoadingOverlay();
            }

            // Update statistics display if available
            if (statistics) {
                this.updateReservationStatistics(safeZone, statistics);
            }

            // Initialize tooltips
            this.initializeReservationTooltips();

        } catch (e) {
            console.error('Error processing enhanced reservations response:', e);
            this.hideLoadingOverlayIfNeeded();
        }
    },

    /**
     * Handle enhanced reservations error
     */
    handleEnhancedReservationsError: function(xhr, status, error) {
        console.error('Error loading enhanced reservations:', error);
        this.hideLoadingOverlayIfNeeded();
        
        // Fallback to basic reservation loading
        if (window.DataLoader && window.DataLoader.loadBookedTables) {
            const selectedDate = $('#myDate').val() || '';
            const currentFloor = sessionStorage.getItem('currentFloor') || '1';
            const formattedDate = window.formatDateForAPI ? window.formatDateForAPI(selectedDate) : selectedDate;
            window.DataLoader.loadBookedTables(formattedDate, currentFloor);
        }
    },

    /**
     * Update reservation status for a specific table
     * @param {string} tableId - The table ID
     * @param {string} newStatus - The new status
     * @param {Object} additionalData - Additional reservation data
     */
    updateReservationStatus: function(tableId, newStatus, additionalData = {}) {
        try {
            console.log(`Updating reservation status for table ${tableId} to ${newStatus}`);

            // Update the visual representation
            if (window.markSingleTableReservation) {
                window.markSingleTableReservation(tableId, newStatus, additionalData);
            }

            // Add animation for status change
            this.animateStatusChange(tableId, newStatus);

            // Update server-side if needed
            if (additionalData.updateServer) {
                this.updateServerReservationStatus(tableId, newStatus, additionalData);
            }

        } catch (e) {
            console.error('Error updating reservation status:', e);
        }
    },

    /**
     * Animate status change for visual feedback
     * @param {string} tableId - The table ID
     * @param {string} status - The new status
     */
    animateStatusChange: function(tableId, status) {
        try {
            const selectors = this.getTableSelectors(tableId);
            const $box = $(selectors.box);

            if ($box.length) {
                // Add animation class based on status
                $box.addClass('status-change-animation');
                
                // Remove animation class after animation completes
                setTimeout(() => {
                    $box.removeClass('status-change-animation');
                }, 1500);

                // Add specific animation for new bookings
                if (status === 'booked' || status === 'confirmed') {
                    $box.addClass('new-booking');
                    setTimeout(() => {
                        $box.removeClass('new-booking');
                    }, 4500);
                }
            }
        } catch (e) {
            console.error('Error animating status change:', e);
        }
    },

    /**
     * Update reservation statistics display
     * @param {string} zone - The zone/floor number
     * @param {Array} statistics - The statistics data
     */
    updateReservationStatistics: function(zone, statistics) {
        try {
            const $statsContainer = $(`#floor${zone}-stats`);
            if (!$statsContainer.length) return;

            let totalBookings = 0;
            let totalAmount = 0;
            let totalAdults = 0;
            let totalChildren = 0;
            let statusCounts = {};

            statistics.forEach(stat => {
                totalBookings += parseInt(stat.count) || 0;
                totalAmount += parseFloat(stat.total_amount) || 0;
                totalAdults += parseInt(stat.total_adults) || 0;
                totalChildren += parseInt(stat.total_children) || 0;
                statusCounts[stat.payment_status] = parseInt(stat.count) || 0;
            });

            // Update statistics display
            const statsHtml = `
                <div class="reservation-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Bookings:</span>
                        <span class="stat-value">${totalBookings}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Amount:</span>
                        <span class="stat-value">$${totalAmount.toFixed(2)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Adults:</span>
                        <span class="stat-value">${totalAdults}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Children:</span>
                        <span class="stat-value">${totalChildren}</span>
                    </div>
                </div>
            `;

            $statsContainer.html(statsHtml);
        } catch (e) {
            console.error('Error updating reservation statistics:', e);
        }
    },

    /**
     * Initialize tooltips for reservation labels
     */
    initializeReservationTooltips: function() {
        try {
            // Initialize Bootstrap tooltips
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        } catch (e) {
            console.error('Error initializing reservation tooltips:', e);
        }
    },

    /**
     * Get table selectors for different table ID formats
     * @param {string} tableId - The table ID
     * @returns {Object} - Object with box and rect selectors
     */
    getTableSelectors: function(tableId) {
        if (tableId.includes('/')) {
            const escapedTable = tableId.replace('/', '\\/');
            return {
                box: `#box-${escapedTable}`,
                rect: `#rbox-${escapedTable}`
            };
        }
        return {
            box: `#box-${tableId}`,
            rect: `#rbox-${tableId}`
        };
    },

    /**
     * Format date for API calls
     * @param {string} dateString - The date string to format
     * @returns {string} - The formatted date string
     */
    formatDate: function(dateString) {
        if (!dateString) return '';
        
        // If already in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            return dateString;
        }
        
        // If in DD-MM-YYYY format, convert to YYYY-MM-DD
        if (/^\d{2}-\d{2}-\d{4}$/.test(dateString)) {
            const parts = dateString.split('-');
            return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        
        return dateString;
    },

    /**
     * Validate zone parameter
     * @param {string} zone - The zone to validate
     * @returns {string} - The validated zone
     */
    validateZone: function(zone) {
        const validZones = ['1', '2', '3', '2b'];
        return validZones.includes(String(zone)) ? String(zone) : '1';
    },

    /**
     * Hide loading overlay if needed
     */
    hideLoadingOverlayIfNeeded: function() {
        if (!window.updatingAllFloors) {
            window.hideLoadingOverlay();
        }
    }
};

// Make functions globally available for backward compatibility
window.loadEnhancedReservations = window.ReservationManager.loadEnhancedReservations.bind(window.ReservationManager);
window.updateReservationStatus = window.ReservationManager.updateReservationStatus.bind(window.ReservationManager);
