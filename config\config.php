<?php
/**
 * Main Configuration File
 *
 * This file contains all the configuration settings for the application.
 */

// Error reporting settings
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to users in production

// Database configuration localhost
define('DB_HOST', 'localhost');
define('DB_NAME', 'kp_service');
define('DB_USER', 'root');
define('DB_PASS', 'kokoku123#@!');
define('DB_CHARSET', 'utf8mb4');

// Database configuration server
// define('DB_HOST', 'localhost');
// define('DB_NAME', 'kp_service');
// define('DB_USER', 'mrkp');
// define('DB_PASS', 'FdFdhk0123#@!');
// define('DB_CHARSET', 'utf8mb4');

// Application paths
define('BASE_PATH', dirname(__DIR__));
define('ASSETS_PATH', '/assets');
define('UPLOADS_PATH', BASE_PATH . '/uploads');
define('JSON_PATH', BASE_PATH . '/json');

// Session settings
define('SESSION_LIFETIME', 3600); // 1 hour
define('SESSION_NAME', 'sawasdee_session');
define('SESSION_SECURE', false); // Set to true if using HTTPS
define('SESSION_HTTP_ONLY', true);

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_ALGO', PASSWORD_ARGON2ID);
define('PASSWORD_OPTIONS', [
    'memory_cost' => 65536, // 64MB
    'time_cost' => 4,       // 4 iterations
    'threads' => 3          // 3 threads
]);

// Application settings
define('DEFAULT_TIMEZONE', 'Asia/Bangkok');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('ITEMS_PER_PAGE', 20);

// Set default timezone
date_default_timezone_set(DEFAULT_TIMEZONE);
