/**
 * Debug script for date change issues
 */

$(document).ready(function() {
    console.log('Date debug script loaded');

    // Add a click handler to the datepicker icon to ensure it works
    $('.input-group-text').off('click').on('click', function() {
        console.log('Calendar icon clicked');
        $('#myDate').datepicker('show');
    });

    // Add a manual change trigger button for testing
    $('<button id="test-date-change" class="btn btn-sm btn-info ms-2">Test Date Change</button>')
        .insertAfter('#myDate')
        .on('click', function() {
            // Get current date
            const currentDate = $('#myDate').val();
            console.log('Current date:', currentDate);
            
            // Parse the date
            const parts = currentDate.split('-');
            const day = parseInt(parts[0]);
            const month = parseInt(parts[1]) - 1;
            const year = parseInt(parts[2]);
            
            // Create a new date one day in the future
            const newDate = new Date(year, month, day + 1);
            const newDay = newDate.getDate().toString().padStart(2, '0');
            const newMonth = (newDate.getMonth() + 1).toString().padStart(2, '0');
            const newYear = newDate.getFullYear();
            
            // Format the new date
            const formattedDate = `${newDay}-${newMonth}-${newYear}`;
            console.log('Setting new date:', formattedDate);
            
            // Set the new date
            $('#myDate').val(formattedDate);
            
            // Trigger the change event
            $('#myDate').trigger('change');
        });

    // Monitor the date change event
    $('#myDate').off('change.debug').on('change.debug', function() {
        console.log('Date change event detected in debug script');
        console.log('New date value:', $(this).val());
    });
});
