<?php
/**
 * <PERSON><PERSON><PERSON> to create default users with proper password hashing
 * Run this script once to set up the initial user accounts
 */

// Include database configuration
require_once dirname(__DIR__) . '/config/config.php';

// Password hashing options
define('PASSWORD_OPTIONS', [
    'memory_cost' => 65536, // 64MB
    'time_cost' => 4,       // 4 iterations
    'threads' => 3          // 3 threads
]);

try {
    // Connect to the database
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

    echo "<h2>Setting up User Management System</h2>";

    // First, update the table structure
    echo "<h3>1. Updating table structure...</h3>";
    
    try {
        $alterSql = "ALTER TABLE `kp_login` 
                     MODIFY COLUMN `role` ENUM('super_admin', 'admin', 'user') NOT NULL DEFAULT 'user'";
        $pdo->exec($alterSql);
        echo "<p style='color: green;'>✅ Table structure updated successfully!</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
            strpos($e->getMessage(), 'already exists') !== false) {
            echo "<p style='color: orange;'>⚠️ Table structure already up to date.</p>";
        } else {
            throw $e;
        }
    }

    // Define default users
    $defaultUsers = [
        [
            'username' => 'superadmin',
            'password' => 'superadmin123',
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'role' => 'super_admin',
            'user_key' => 'superadmin_' . bin2hex(random_bytes(8))
        ],
        [
            'username' => 'admin',
            'password' => 'admin123',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'user_key' => 'admin_' . bin2hex(random_bytes(8))
        ],
        [
            'username' => 'user',
            'password' => 'user123',
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'role' => 'user',
            'user_key' => 'user_' . bin2hex(random_bytes(8))
        ]
    ];

    echo "<h3>2. Creating default users...</h3>";

    foreach ($defaultUsers as $userData) {
        // Check if user already exists
        $checkSql = "SELECT id FROM kp_login WHERE user = :username";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([':username' => $userData['username']]);
        
        if ($checkStmt->fetch()) {
            echo "<p style='color: orange;'>⚠️ User '{$userData['username']}' already exists, skipping...</p>";
            continue;
        }

        // Hash the password
        $hashedPassword = password_hash($userData['password'], PASSWORD_ARGON2ID, PASSWORD_OPTIONS);

        // Insert the user
        $insertSql = "INSERT INTO kp_login (user, pass, name, email, role, status, user_key, create_date) 
                      VALUES (:username, :password, :name, :email, :role, 1, :user_key, NOW())";
        
        $insertStmt = $pdo->prepare($insertSql);
        $result = $insertStmt->execute([
            ':username' => $userData['username'],
            ':password' => $hashedPassword,
            ':name' => $userData['name'],
            ':email' => $userData['email'],
            ':role' => $userData['role'],
            ':user_key' => $userData['user_key']
        ]);

        if ($result) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
            echo "✅ <strong>{$userData['role']}</strong> user created successfully!<br>";
            echo "Username: <strong>{$userData['username']}</strong><br>";
            echo "Password: <strong>{$userData['password']}</strong><br>";
            echo "Email: <strong>{$userData['email']}</strong>";
            echo "</div>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create user '{$userData['username']}'</p>";
        }
    }

    // Display current users
    echo "<h3>3. Current Users in System:</h3>";
    $usersSql = "SELECT id, user, name, email, role, status, create_date FROM kp_login ORDER BY role DESC, id ASC";
    $usersStmt = $pdo->query($usersSql);
    $users = $usersStmt->fetchAll();

    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Name</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";

        foreach ($users as $user) {
            $roleColor = '';
            switch ($user['role']) {
                case 'super_admin':
                    $roleColor = 'background: #dc3545; color: white;';
                    break;
                case 'admin':
                    $roleColor = 'background: #fd7e14; color: white;';
                    break;
                case 'user':
                    $roleColor = 'background: #28a745; color: white;';
                    break;
            }

            $statusColor = $user['status'] ? 'color: green;' : 'color: red;';
            $statusText = $user['status'] ? 'Active' : 'Inactive';

            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'><strong>{$user['user']}</strong></td>";
            echo "<td style='padding: 8px;'>{$user['name']}</td>";
            echo "<td style='padding: 8px;'>{$user['email']}</td>";
            echo "<td style='padding: 8px; {$roleColor}'>" . ucfirst(str_replace('_', ' ', $user['role'])) . "</td>";
            echo "<td style='padding: 8px; {$statusColor}'>{$statusText}</td>";
            echo "<td style='padding: 8px;'>{$user['create_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h3>4. Role Permissions:</h3>";
    echo "<ul>";
    echo "<li><strong>Super Admin:</strong> Full system access, can manage all users and settings</li>";
    echo "<li><strong>Admin:</strong> Can manage bookings, view reports, manage regular users</li>";
    echo "<li><strong>User:</strong> Can view and create bookings, limited access</li>";
    echo "</ul>";

    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border: 1px solid #bee5eb; margin: 20px 0;'>";
    echo "<h4>✅ User Management System Setup Complete!</h4>";
    echo "<p>You can now log in with any of the created accounts. Remember to change the default passwords after first login.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test login with each user type</li>";
    echo "<li>Implement role-based access control in your application</li>";
    echo "<li>Change default passwords</li>";
    echo "<li>Set up user management interface</li>";
    echo "</ul>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4>❌ Database Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4>❌ General Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
