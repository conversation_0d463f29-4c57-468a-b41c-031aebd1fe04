Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8FB560000 ntdll.dll
7FF8D5870000 aswhook.dll
7FF8FA990000 KERNEL32.DLL
7FF8F8D70000 KERNELBASE.dll
7FF8F9860000 USER32.dll
7FF8F9220000 win32u.dll
000210040000 msys-2.0.dll
7FF8FB460000 GDI32.dll
7FF8F8C30000 gdi32full.dll
7FF8F9250000 msvcp_win.dll
7FF8F8A40000 ucrtbase.dll
7FF8FA170000 advapi32.dll
7FF8F9A30000 msvcrt.dll
7FF8FA0B0000 sechost.dll
7FF8FB080000 RPCRT4.dll
7FF8F7C90000 CRYPTBASE.DLL
7FF8F8B90000 bcryptPrimitives.dll
7FF8F9E40000 IMM32.DLL
