-- Drop the kp_tables table if it exists
DROP TABLE IF EXISTS `kp_tables`;
-- Create the kp_tables table
CREATE TABLE `kp_tables` (
  `table_id` VARCHAR(10) NOT NULL,
  `x_position` INT NOT NULL,
  `y_position` INT NOT NULL,
  `width` INT NOT NULL,
  `height` INT NOT NULL,
  `status` ENUM('active', 'disabled') NOT NULL DEFAULT 'active',
  `disabled_until` DATE NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`table_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;
-- Insert initial data for tables A1/1-A1/15 (formerly 1A01-1A15)
INSERT INTO `kp_tables` (
    `table_id`,
    `x_position`,
    `y_position`,
    `width`,
    `height`,
    `status`
  )
VALUES -- Row A1 (y=76)
  ('A1/1', 383, 76, 54, 36, 'active'),
  ('A1/2', 440, 76, 54, 36, 'active'),
  ('A1/3', 497, 76, 54, 36, 'active'),
  ('A1/4', 554, 76, 54, 36, 'active'),
  ('A1/5', 611, 76, 54, 36, 'active'),
  ('A1/6', 668, 76, 54, 36, 'active'),
  ('A1/7', 725, 76, 54, 36, 'active'),
  ('A1/8', 782, 76, 54, 36, 'active'),
  ('A1/9', 839, 76, 54, 36, 'active'),
  ('A1/10', 896, 76, 54, 36, 'active'),
  ('A1/11', 953, 76, 54, 36, 'active'),
  ('A1/12', 1010, 76, 54, 36, 'active'),
  ('A1/13', 1067, 76, 54, 36, 'active'),
  ('A1/14', 1124, 76, 54, 36, 'active'),
  ('A1/15', 1181, 76, 54, 36, 'active'),
  -- Row A2 (y=115) (formerly 1B)
  ('A2/1', 383, 115, 54, 36, 'active'),
  ('A2/2', 440, 115, 54, 36, 'active'),
  ('A2/3', 497, 115, 54, 36, 'active'),
  ('A2/4', 554, 115, 54, 36, 'active'),
  ('A2/5', 611, 115, 54, 36, 'active'),
  ('A2/6', 668, 115, 54, 36, 'active'),
  ('A2/7', 725, 115, 54, 36, 'active'),
  ('A2/8', 782, 115, 54, 36, 'active'),
  ('A2/9', 839, 115, 54, 36, 'active'),
  ('A2/10', 896, 115, 54, 36, 'active'),
  ('A2/11', 953, 115, 54, 36, 'active'),
  ('A2/12', 1010, 115, 54, 36, 'active'),
  ('A2/13', 1067, 115, 54, 36, 'active'),
  ('A2/14', 1124, 115, 54, 36, 'active'),
  ('A2/15', 1181, 115, 54, 36, 'active'),
  -- Row A3 (y=154) (formerly 1C)
  ('A3/1', 383, 154, 54, 36, 'active'),
  ('A3/2', 440, 154, 54, 36, 'active'),
  ('A3/3', 497, 154, 54, 36, 'active'),
  ('A3/4', 554, 154, 54, 36, 'active'),
  ('A3/5', 611, 154, 54, 36, 'active'),
  ('A3/6', 668, 154, 54, 36, 'active'),
  ('A3/7', 725, 154, 54, 36, 'active'),
  ('A3/8', 782, 154, 54, 36, 'active'),
  ('A3/9', 839, 154, 54, 36, 'active'),
  ('A3/10', 896, 154, 54, 36, 'active'),
  ('A3/11', 953, 154, 54, 36, 'active'),
  ('A3/12', 1010, 154, 54, 36, 'active'),
  ('A3/13', 1067, 154, 54, 36, 'active'),
  ('A3/14', 1124, 154, 54, 36, 'active'),
  ('A3/15', 1181, 154, 54, 36, 'active'),
  -- Row A4 (y=194) (formerly 1D)
  ('A4/1', 383, 194, 54, 36, 'active'),
  ('A4/2', 440, 194, 54, 36, 'active'),
  ('A4/3', 497, 194, 54, 36, 'active'),
  ('A4/4', 554, 194, 54, 36, 'active'),
  ('A4/5', 611, 194, 54, 36, 'active'),
  ('A4/6', 668, 194, 54, 36, 'active'),
  ('A4/7', 725, 194, 54, 36, 'active'),
  ('A4/8', 782, 194, 54, 36, 'active'),
  ('A4/9', 839, 194, 54, 36, 'active'),
  ('A4/10', 896, 194, 54, 36, 'active'),
  ('A4/11', 953, 194, 54, 36, 'active'),
  ('A4/12', 1010, 194, 54, 36, 'active'),
  ('A4/13', 1067, 194, 54, 36, 'active'),
  ('A4/14', 1124, 194, 54, 36, 'active'),
  ('A4/15', 1181, 194, 54, 36, 'active'),
  -- Row A5 (y=326) (formerly 1E)
  ('A5/1', 383, 326, 54, 36, 'active'),
  ('A5/2', 440, 326, 54, 36, 'active'),
  ('A5/3', 497, 326, 54, 36, 'active'),
  ('A5/4', 554, 326, 54, 36, 'active'),
  ('A5/5', 611, 326, 54, 36, 'active'),
  ('A5/6', 668, 326, 54, 36, 'active'),
  ('A5/7', 725, 326, 54, 36, 'active'),
  ('A5/8', 782, 326, 54, 36, 'active'),
  ('A5/9', 839, 326, 54, 36, 'active'),
  ('A5/10', 896, 326, 54, 36, 'active'),
  ('A5/11', 953, 326, 54, 36, 'active'),
  ('A5/12', 1010, 326, 54, 36, 'active'),
  ('A5/13', 1067, 326, 54, 36, 'active'),
  ('A5/14', 1124, 326, 54, 36, 'active'),
  ('A5/15', 1181, 326, 54, 36, 'active'),
  -- Row A6 (y=365) (formerly 1F)
  ('A6/1', 383, 365, 54, 36, 'active'),
  ('A6/2', 440, 365, 54, 36, 'active'),
  ('A6/3', 497, 365, 54, 36, 'active'),
  ('A6/4', 554, 365, 54, 36, 'active'),
  ('A6/5', 611, 365, 54, 36, 'active'),
  ('A6/6', 668, 365, 54, 36, 'active'),
  ('A6/7', 725, 365, 54, 36, 'active'),
  ('A6/8', 782, 365, 54, 36, 'active'),
  ('A6/9', 839, 365, 54, 36, 'active'),
  ('A6/10', 896, 365, 54, 36, 'active'),
  ('A6/11', 953, 365, 54, 36, 'active'),
  ('A6/12', 1010, 365, 54, 36, 'active'),
  ('A6/13', 1067, 365, 54, 36, 'active'),
  ('A6/14', 1124, 365, 54, 36, 'active'),
  ('A6/15', 1181, 365, 54, 36, 'active'),
  -- Row A7 (y=404) (formerly 1G)
  ('A7/1', 383, 404, 54, 36, 'active'),
  ('A7/2', 440, 404, 54, 36, 'active'),
  ('A7/3', 497, 404, 54, 36, 'active'),
  ('A7/4', 554, 404, 54, 36, 'active'),
  ('A7/5', 611, 404, 54, 36, 'active'),
  ('A7/6', 668, 404, 54, 36, 'active'),
  ('A7/7', 725, 404, 54, 36, 'active'),
  ('A7/8', 782, 404, 54, 36, 'active'),
  ('A7/9', 839, 404, 54, 36, 'active'),
  ('A7/10', 896, 404, 54, 36, 'active'),
  ('A7/11', 953, 404, 54, 36, 'active'),
  ('A7/12', 1010, 404, 54, 36, 'active'),
  ('A7/13', 1067, 404, 54, 36, 'active'),
  ('A7/14', 1124, 404, 54, 36, 'active'),
  ('A7/15', 1181, 404, 54, 36, 'active'),
  -- Row A8 (y=443) (formerly 1H)
  ('A8/1', 383, 443, 54, 36, 'active'),
  ('A8/2', 440, 443, 54, 36, 'active'),
  ('A8/3', 497, 443, 54, 36, 'active'),
  ('A8/4', 554, 443, 54, 36, 'active'),
  ('A8/5', 611, 443, 54, 36, 'active'),
  ('A8/6', 668, 443, 54, 36, 'active'),
  ('A8/7', 725, 443, 54, 36, 'active'),
  ('A8/8', 782, 443, 54, 36, 'active'),
  ('A8/9', 839, 443, 54, 36, 'active'),
  ('A8/10', 896, 443, 54, 36, 'active'),
  ('A8/11', 953, 443, 54, 36, 'active'),
  ('A8/12', 1010, 443, 54, 36, 'active'),
  ('A8/13', 1067, 443, 54, 36, 'active'),
  ('A8/14', 1124, 443, 54, 36, 'active'),
  ('A8/15', 1181, 443, 54, 36, 'active') ON DUPLICATE KEY
UPDATE `x_position` =
VALUES(`x_position`),
  `y_position` =
VALUES(`y_position`),
  `width` =
VALUES(`width`),
  `height` =
VALUES(`height`);