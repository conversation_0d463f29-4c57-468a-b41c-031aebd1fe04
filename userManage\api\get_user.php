<?php
session_start();
require_once '../../includes/role_check.php';
require_once '../../dbconnect/_dbconnect.php';

// Check if user is logged in and has admin access
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

$userId = (int)$_GET['id'];

try {
    // Get database connection
    $pdo = db_connect();

    // Get user from database
    $stmt = $pdo->prepare("SELECT id, user as username, email, name, role, status, created_at FROM kp_login WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Format the data
    $user['status'] = (int)$user['status'];
    
    if ($user['created_at']) {
        $user['created_at'] = date('Y-m-d H:i:s', strtotime($user['created_at']));
    }
    
    echo json_encode([
        'success' => true,
        'user' => $user
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
