<?php
session_start();
require_once '../../includes/role_check.php';
require_once '../../dbconnect/_dbconnect.php';

// Check if user is logged in and has admin access
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Check if ID is provided
if (!isset($_POST['id']) || empty($_POST['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit;
}

$userId = (int)$_POST['id'];

// Prevent users from deleting themselves
if ($userId == $_SESSION['user_id']) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'You cannot delete your own account']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();

    // Check if user exists and get their role
    $stmt = $pdo->prepare("SELECT id, role FROM kp_login WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Only super admin can delete admin users
    if ($user['role'] === 'super_admin' || ($user['role'] === 'admin' && $_SESSION['role'] !== 'super_admin')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'You do not have permission to delete this user']);
        exit;
    }
    
    // Delete the user
    $stmt = $pdo->prepare("DELETE FROM kp_login WHERE id = ?");
    $result = $stmt->execute([$userId]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete user']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
