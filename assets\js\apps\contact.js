$(function () {
  // Check all functionality
  function checkall(clickchk, relChkbox) {
    var checker = $("#" + clickchk);
    var multichk = $("." + relChkbox);

    checker.click(function () {
      multichk.prop("checked", $(this).prop("checked"));
      $(".show-btn").toggle();
    });
  }

  checkall("contact-check-all", "contact-chkbox");

  // Search functionality in table
  $("#input-search").on("keyup", function () {
    var rex = new RegExp($(this).val(), "i");
    $(".search-table .search-items:not(.header-item)").hide();
    $(".search-table .search-items:not(.header-item)")
      .filter(function () {
        return rex.test($(this).text());
      })
      .show();
  });

  // Show add contact modal
  $("#btn-add-contact").on("click", function (event) {
    $("#addContactModal .modal-title").text("New Booking");

    // Reset form fields
    $("#addContactModal form")[0].reset();

    // Reset special request radio buttons to "None"
    $("input[name='c-special-request']").prop('checked', false);
    $("#c-special-request-none").prop('checked', true);

    // Set default date to today
    var today = new Date();
    var formattedDate = today.toISOString().split('T')[0];
    $("#addContactModal #c-useDate").val(formattedDate);

    // Clear selected tables display
    $("#selected-tables-display").text("");

    // Show add button, hide edit button
    $("#addContactModal #btn-add").show();
    $("#addContactModal #btn-edit").hide();

    // Show the modal
    $("#addContactModal").modal("show");
  });

  // Handling the modal display for edit and adding new contact
  $('#addContactModal').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget); // Button that triggered the modal
    var modal = $(this);

    // Determine if it is an edit action
    if (button && button.data('action') === 'edit') {
      // Set modal for editing with values
      modal.find('#c-name').val(button.data('name'));
      modal.find('#c-phone').val(button.data('phone'));
      modal.find('#c-adult').val(button.data('adult'));
      modal.find('#c-child').val(button.data('child'));
      modal.find('#c-infant').val(button.data('infant'));
      modal.find('#c-guide').val(button.data('guide'));
      modal.find('#c-foc').val(button.data('foc'));
      modal.find('#c-tl').val(button.data('tl'));
      modal.find('#c-voucher').val(button.data('voucher'));
      modal.find('#c-agent').val(button.data('agent'));
      modal.find('#c-remark').val(button.data('remark'));
      modal.find('#c-amount').val(button.data('amount'));
      modal.find('#c-special-request').val(button.data('special-request'));


     

      modal.find('#btn-add').hide();
      modal.find('#btn-edit').show();
    } else {
      // Set modal for adding with blank fields
      modal.find('input').val('');
      // Reset special request radio buttons to "None" for new bookings
      modal.find("input[name='c-special-request']").prop('checked', false);
      modal.find("#c-special-request-none").prop('checked', true);
      modal.find('#btn-add').show();
      modal.find('#btn-edit').hide();
    }
  });

  // Adding a new contact
  $(document).on("click", "#btn-add", function () {
    var getParent = $(this).parents(".modal-content");
    var data = {
      name: getParent.find("#c-name").val(),
      phone: getParent.find("#c-phone").val(),
      adult: getParent.find("#c-adult").val(),
      child: getParent.find("#c-child").val(),
      infant: getParent.find("#c-infant").val(),
      guide: getParent.find("#c-guide").val(),
      foc: getParent.find("#c-foc").val(),
      tl: getParent.find("#c-tl").val(),
      floor: getParent.find("#c-floor").val(),
      table: getParent.find("#c-table").val(),
      voucher: getParent.find("#c-voucher").val(),
      agent: getParent.find("#c-agent").val(),
      remark: getParent.find("#c-remark").val(),
      special_request: getParent.find("input[name='c-special-request']:checked").val(),
      useDate: getParent.find("#c-useDate").val(),
      useZone: getParent.find("#c-useZone").val(),
    };

    $.ajax({
      type: "POST",
      url: "../api/addBooking.php",
      data: data,
      success: function(response) {
        alert("Contact added: " + response);
        location.reload();  // Reload the page or update the table as needed
      },
      error: function() {
        alert("Error adding contact");
      }
    });
  });

  // Handling the edit button click to open the modal with pre-filled data
  $(document).on("click", ".edit", function () {
    // Get the data from the row
    var row = $(this).closest(".search-items");

    var useDate = row.find(".user-date[data-usedate]").data("usedate");
    var useZone = row.find(".user-zoneid[data-zoneid]").data("zoneid");

    var name = row.find(".user-name[data-name]").data("name");
    var phone = row.find(".user-ph-no[data-phone]").data("phone");

    var adult = row.find(".user-adult[data-adult]").data("adult");
    var child = row.find(".user-child[data-child]").data("child");
    var infant = row.find(".user-infant[data-infant]").data("infant");

    var guide = row.find(".user-guide[data-guide]").data("guide");
    var foc = row.find(".user-foc[data-foc]").data("foc");
    var tl = row.find(".user-tl[data-tl]").data("tl");

    var floor = row.find(".user-floor[data-floor]").data("floor");
    var table = row.find(".user-table[data-table]").data("table");

    var voucher = row.find(".user-voucher[data-voucher]").data("voucher");
    var agent = row.find(".user-agent[data-agent]").data("agent");

    var remark = row.find(".user-remark[data-remark]").data("remark");
    var amount = row.find(".user-amount[data-amount]").data("amount");
    var specialRequest = row.find(".user-booking-special[data-special-request]").data("special-request");
    // console.log("Special Request:", specialRequest, "Type:", typeof specialRequest); // Debugging
    
    var id = row.find(".user-bookid[data-id]").data("id");
    var orderId = row.find(".user-orderid[data-orderid]").data("orderid");

    // Open the modal
    $("#addContactModal").modal("show");
    $("#addContactModal #btn-add").hide();
    $("#addContactModal #btn-edit").show();
    $("#addContactModal #btn-edit").data("id", id);
    $("#addContactModal #btn-edit").data("action", "edit");
    $("#addContactModal #c-name").val(name);
    $("#addContactModal #c-phone").val(phone);
    $("#addContactModal #c-adult").val(adult);
    $("#addContactModal #c-child").val(child);
    $("#addContactModal #c-infant").val(infant);
    $("#addContactModal #c-guide").val(guide);
    $("#addContactModal #c-foc").val(foc);
    $("#addContactModal #c-tl").val(tl);
    $("#addContactModal #c-voucher").val(voucher);
    $("#addContactModal #c-agent").val(agent);
    $("#addContactModal #c-remark").val(remark);
    $("#addContactModal #c-amount").val(amount);
    $("#addContactModal #c-orderid").val(orderId);
    $("#addContactModal #c-id").val(id);
    $("#addContactModal #c-special-request").val(specialRequest);
    
    
    

    // Format date for the date input (YYYY-MM-DD)
    if (useDate) {
      var dateObj = new Date(useDate);
      var formattedDate = dateObj.toISOString().split('T')[0];
      $("#addContactModal #c-useDate").val(formattedDate);
    } else {
      $("#addContactModal #c-useDate").val(useDate);
    }

    $("#addContactModal #c-useZone").val(useZone);
    $("#addContactModal #c-floor").val(floor);
    $("#addContactModal #c-table").val(table);

    // Update the selected tables display
    $("#selected-tables-display").text(table);

    console.log("Special Request:", specialRequest, "Type:", typeof specialRequest); // Debugging

    if (specialRequest === 0) {
      $("#addContactModal #c-special-request-none").prop('checked', true);
    } else if (specialRequest === 1) {
      $("#addContactModal #c-special-request-birthday").prop('checked', true);
    } else if (specialRequest === 2) {
      $("#addContactModal #c-special-request-anniversary").prop('checked', true);
      console.log("Radio button set successfully");
    }

   

    // If the table value is very long, make sure the modal adjusts
    if (table && table.length > 100) {
      // Ensure the modal body has enough space
      $("#addContactModal .modal-dialog").addClass("modal-lg");
    } else {
      $("#addContactModal .modal-dialog").removeClass("modal-lg");
    }

    // Update modal title
    $("#addContactModal .modal-title").text("Edit Booking");
  });

  // Edit a contact
  $(document).on("click", "#btn-edit", function () {
    var getParent = $(this).parents(".modal-content");
    var data = {
      id: $(this).data("id"),
      useDate: getParent.find("#c-useDate").val(),
      useZone: getParent.find("#c-useZone").val(),

      name: getParent.find("#c-name").val(),
      phone: getParent.find("#c-phone").val(),

      adult: getParent.find("#c-adult").val(),
      child: getParent.find("#c-child").val(),
      infant: getParent.find("#c-infant").val(),

      guide: getParent.find("#c-guide").val(),
      foc: getParent.find("#c-foc").val(),
      tl: getParent.find("#c-tl").val(),

      floor: getParent.find("#c-floor").val(),
      table: getParent.find("#c-table").val(),

      voucher: getParent.find("#c-voucher").val(),
      agent: getParent.find("#c-agent").val(),
      amount: getParent.find("#c-amount").val(),
      remark: getParent.find("#c-remark").val(),
      special_request: getParent.find("input[name='c-special-request']:checked").val(),
    };

    $.ajax({
      type: "POST",
      url: "../api/editBooking.php",
      data: data,
      success: function(response) {
        // check resposne json format
        // var res = JSON.parse(response);
        var res = response;
        if (res.status === "success") {
          // alert("Contact edited successfully");
          Swal.fire({
            icon: 'success',
            title: 'Contact edited successfully',
            text: res.message,
            timer: 2000,
            timerProgressBar: true, // Optional: shows a timer progress bar
              willClose: () => {
                location.reload();
              }
          });

        } else if (res.status === "error") {
          // alert("Error editing contact: " + res.message);
          Swal.fire({
            icon: 'error',
            title: 'Error editing contact',
            text: res.message,
          });
        }
        // location.reload();  // Reload the page or update the table as needed
      },
      error: function() {
        alert("Error editing contact");
      }
    });
  });


  // Delete a contact
  $(document).on("click", ".delete", function () {
    var getParent = $(this).parents(".search-items");
    var data = {
      id: getParent.find(".user-bookid[data-id]").data("id"),
    };
    console.log('Deleting booking with data:', data);

    // Show confirmation dialog before deleting
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this booking deletion!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        // Show loading indicator
        Swal.fire({
          title: 'Deleting booking...',
          text: 'Please wait',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        // Send an AJAX request to delete the booking
        $.ajax({
          type: "POST",
          url: "../api/deleteBooking.php",
          data: data,
          dataType: 'json', // Specify JSON dataType to avoid parsing issues
          success: function(response) {
            console.log('Delete response:', response);
            if (response.status === "success") {
              Swal.fire({
                icon: 'success',
                title: 'Booking deleted successfully',
                text: response.message,
                timer: 2000,
                timerProgressBar: true,
                willClose: () => {
                  location.reload();
                }
              });
            } else if (response.status === "error") {
              Swal.fire({
                icon: 'error',
                title: 'Error deleting booking',
                text: response.message,
              });
            }
          },
          error: function(xhr, status, error) {
            console.error('Delete error:', xhr.responseText, status, error);
            Swal.fire({
              icon: 'error',
              title: 'Error deleting booking',
              text: 'An error occurred while deleting the booking. Please try again.',
            });
          }
        });
      }
    });
    // $(this).closest(".search-items").remove();
  });


  // Delete multiple selected contacts
  $(".delete-multiple").on("click", function () {
    $(".contact-chkbox:checked").closest(".search-items").remove();
  });

  // function get json data
  function getJsonData(url) {
    return $.ajax({
      type: "GET",
      url: url,
      dataType: "json",
    });
  }

  // Example usage of getJsonData
  getJsonData("../api/CreateFileByDate.php?date" + new Date().toISOString().slice(0, 10))
  .then(function (data) {
    console.log(data);
  })
  .catch(function (error) {
    console.error("Error fetching JSON data:", error);
  });

  // Update selected tables display when table input changes
  $("#c-table").on("input", function() {
    var tableValue = $(this).val();
    $("#selected-tables-display").text(tableValue);

    // If the table value is very long, make sure the modal adjusts
    if (tableValue && tableValue.length > 100) {
      // Ensure the modal body has enough space
      $("#addContactModal .modal-dialog").addClass("modal-lg");
    } else {
      $("#addContactModal .modal-dialog").removeClass("modal-lg");
    }
  });

  // Format date when date input changes
  $("#c-useDate").on("change", function() {
    var dateValue = $(this).val();
    if (dateValue) {
      var dateObj = new Date(dateValue);
      var day = String(dateObj.getDate()).padStart(2, '0');
      var month = String(dateObj.getMonth() + 1).padStart(2, '0');
      var year = dateObj.getFullYear();
      var formattedDate = day + '-' + month + '-' + year;
      console.log("Date changed to: " + formattedDate);
    }
  });



});

