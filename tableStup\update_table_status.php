<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Validate input
    if (!isset($_POST['id']) || !isset($_POST['status'])) {
        throw new Exception('Missing required parameters');
    }

    $tableId = $_POST['id'];
    $status = $_POST['status'];
    $disabledUntil = isset($_POST['disabled_until']) ? $_POST['disabled_until'] : null;

    // Validate status
    if ($status !== 'active' && $status !== 'disabled') {
        throw new Exception('Invalid status value');
    }

    // Validate date format if provided
    if ($disabledUntil !== null && $disabledUntil !== '') {
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $disabledUntil)) {
            throw new Exception('Invalid date format. Use YYYY-MM-DD');
        }

        // Check if date is in the future
        $disabledDate = new DateTime($disabledUntil);
        $disabledDate->setTime(0, 0, 0); // Set time to beginning of day

        $today = new DateTime();
        $today->setTime(0, 0, 0); // Set time to beginning of day

        $tomorrow = clone $today;
        $tomorrow->modify('+1 day');

        if ($disabledDate <= $today) {
            throw new Exception('Disabled until date must be in the future');
        }
    }

    // Connect to database
    $conn = db_connect();

    // Check if table exists
    $checkSql = "SELECT COUNT(*) FROM kp_tables WHERE table_id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':id', $tableId);
    $checkStmt->execute();

    if ($checkStmt->fetchColumn() == 0) {
        throw new Exception('Table not found');
    }

    // Update table status
    if ($status === 'active') {
        // If activating, clear the disabled_until date
        $sql = "UPDATE kp_tables SET status = :status, disabled_until = NULL WHERE table_id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $tableId);
    } else {
        // If disabling, set the disabled_until date if provided
        $sql = "UPDATE kp_tables SET status = :status, disabled_until = :disabled_until WHERE table_id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $tableId);
        $stmt->bindParam(':disabled_until', $disabledUntil, PDO::PARAM_STR);
    }

    $stmt->execute();

    // Return success
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
