
  <script src="../assets/js/vendor.min.js"></script>
  <!-- Import Js Files -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
  <script src="../assets/js/theme/sidebarmenu.js"></script>

  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
  <script src="../assets/libs/fullcalendar/index.global.min.js"></script>
  <script src="../assets/js/apps/contact.js"></script>
  <script src="../assets/libs/prismjs/prism.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->
  <script src="js/agent.js"></script> <!-- Agent Management Script -->

  <script>
  function formatAndDisplayDate() {
    var input = document.getElementById('myDate');
    if (input.value) {
        var date = new Date(input.value);

        // Full date format: "Saturday, March 15, 2025"
        var fullOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        var fullFormattedDate = new Intl.DateTimeFormat('en-US', fullOptions).format(date);
        document.getElementById('fullDate').innerText = fullFormattedDate;

        // Short date format: "14-03-2025"
        var shortOptions = { year: 'numeric', month: '2-digit', day: '2-digit' };
        var shortFormattedDate = new Intl.DateTimeFormat('en-GB', shortOptions).format(date).replace(/\//g, '-');
        document.getElementById('shortDate').innerText = shortFormattedDate;

        console.log('Formatted date:', shortFormattedDate);

        // Fetch and display bookings by date
        fetchBookingsByDate(shortFormattedDate);
        getZoneJson(shortFormattedDate);
    } else {
        // If no date is selected, use today's date
        var today = new Date();

        // Set the date input value to today
        var year = today.getFullYear();
        var month = (today.getMonth() + 1).toString().padStart(2, '0');
        var day = today.getDate().toString().padStart(2, '0');
        input.value = year + '-' + month + '-' + day;

        // Call the function again to format and display the date
        formatAndDisplayDate();
    }
  }


  function fetchBookingsByDate(date) {
    console.log('Fetching bookings for date:', date);

    // Show loading indicator
    document.getElementById('output').innerHTML = '<tr><td colspan="11" class="text-center">Loading bookings...</td></tr>';

    // Fetch bookings timestamped with the selected date
    fetch('../api/getAPIbyMonth.php?date=' + date + '&timestamp=' + new Date().getTime())
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.json();
      })
      .then(data => {
        console.log('Bookings data received:', data);
        updateBookingsTable(data);
      })
      .catch(error => {
        console.error('Error fetching bookings:', error);
        document.getElementById('output').innerHTML = '<tr><td colspan="11" class="text-center text-danger">Error loading bookings. Please try again.</td></tr>';

        // Show error notification
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load bookings. ' + error.message,
        });
      });
  }

    // Function to get zone data from JSON file
    function getZoneJson(date) {
        console.log('Fetching zones for date:', date);

        // Make sure we're using the correct parameter name (date)
        fetch('../api/getZone.php?date=' + date + '&timestamp=' + new Date().getTime())
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            console.log('Zone data received:', data);

            // Add data to select option id="c-useZone"
            var select = document.getElementById("c-useZone");
            if (!select) {
                console.error('Zone select element not found');
                return;
            }

            // Clear existing options
            select.innerHTML = "";

            // Add default option
            var option = document.createElement("option");
            option.text = "Zone";
            option.value = "";
            select.add(option);

            // Add zone options
            if (Array.isArray(data)) {
                data.forEach(function(item) {
                    var option = document.createElement("option");
                    option.text = item.zone_name;
                    option.value = item.zone_id;
                    select.add(option);
                });
            } else {
                console.error('Zone data is not an array:', data);
            }
        })
        .catch(error => {
            console.error('Error fetching zones:', error);

            // Show error notification
            Swal.fire({
                icon: 'warning',
                title: 'Warning',
                text: 'Failed to load zone data. ' + error.message,
            });
        });
    }

  function updateBookingsTable(bookings) {
    const tableBody = document.getElementById('output');
    tableBody.innerHTML = '';  // Clear existing rows once

    // Check if bookings is defined and it is an array
    if (bookings && Array.isArray(bookings) && bookings.length > 0) {
        bookings.forEach((booking, index) => {
            try {
                // Validate required booking properties
                if (!booking["Create Date"] || !booking["Use Date"]) {
                    console.error('Booking missing required dates:', booking);
                    return; // Skip this booking
                }

                var order_date = new Date(booking["Create Date"]);
                if (isNaN(order_date.getTime())) {
                    console.error('Invalid Create Date:', booking["Create Date"]);
                    order_date = new Date(); // Use current date as fallback
                }

                // Change date format to 13-03-2025 12:00:00
                var options_order = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' };
                var formattedDate_order = new Intl.DateTimeFormat('en-GB', options_order).format(order_date);

                // Change date booking["Use Date"] format to 13-03-2025
                var use_date = new Date(booking["Use Date"]);
                if (isNaN(use_date.getTime())) {
                    console.error('Invalid Use Date:', booking["Use Date"]);
                    use_date = new Date(); // Use current date as fallback
                }

                var options_use = { year: 'numeric', month: '2-digit', day: '2-digit' };
                var formattedDate_use = new Intl.DateTimeFormat('en-GB', options_use).format(use_date).replace(/\//g, '-');
                var bookingSpecial = "";

                // Check special_request if booking["Request"] is empty then set to "N/A"
                if (booking["Special"] == 1) {
                    bookingSpecial = `<i class="icon ti ti-cake fs-7" title="วันเกิด"></i>`;
                } else if (booking["Special"] == 2) {
                    bookingSpecial = `<i class="icon ti ti-glass-full fs-7" title="วันครบรอบ"></i>`;
                }


                const row = document.createElement('tr');
                row.className = 'search-items';
                row.innerHTML = `
                    <td>
                        <h6 class="user-work fs-3" data-order="${booking["Order No"]}">${booking["Order No"]}</h6>
                        <span class="user-name mb-0">${formattedDate_order}</span>
                    </td>
                    <td>
                        <h6 class="user-name mb-0" data-name="${booking.Customer || 'N/A'}">${booking.Customer || 'N/A'}</h6>
                        <span class="user-ph-no" data-phone="${booking.Phone || 'N/A'}">${booking.Phone || 'N/A'}</span>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Adults" class="text-bg-primary text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Adults || 0}</a>
                            <input type="hidden" class="user-adult" data-adult="${booking.Guests?.Adults || 0}" value="${booking.Guests?.Adults || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Children" class="text-bg-success text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Children || 0}</a>
                            <input type="hidden" class="user-child" data-child="${booking.Guests?.Children || 0}" value="${booking.Guests?.Children || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Infants" class="text-bg-danger text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Infants || 0}</a>
                            <input type="hidden" class="user-infant" data-infant="${booking.Guests?.Infants || 0}" value="${booking.Guests?.Infants || 0}">
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guide || 0}</a>
                            <input type="hidden" class="user-guide" data-guide="${booking.Guide || 0}" value="${booking.Guide || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.FOC || 0}</a>
                            <input type="hidden" class="user-foc" data-foc="${booking.FOC || 0}" value="${booking.FOC || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.TL || 0}</a>
                            <input type="hidden" class="user-tl" data-tl="${booking.TL || 0}" value="${booking.TL || 0}">
                        </div>
                    </td>
                    <td><span class="user-floor mb-0" data-floor="${booking.Floor || ''}">${booking.Floor || ''}</span></td>
                    <td><span class="user-table mb-0" data-table="${booking.Table || ''}">${formatTableData(booking.Table) || ''}</span></td>
                    <td><span class="user-voucher mb-0" data-voucher="${booking["Voucher No"] || ''}">${booking["Voucher No"] || ''}</span></td>
                    <td><span class="user-agent mb-0" data-agent="${booking.Agent || ''}">${booking.Agent || ''}</span></td>
                    <td>
                        <span class="user-booking-special mb-0">${bookingSpecial}</span><br/>
                        <span class="user-remark mb-1 badge text-bg-light" data-remark="${booking.Request || ''}">${booking.Request || ''}</span>
                    </td>
                    <td><span class="mb-0">${formattedDate_use}</span></td>
                    <td>
                        <input type="hidden" class="user-bookid" data-id="${booking["BookID"]}" value="${booking["BookID"]}">
                        <input type="hidden" class="user-zoneid" data-zoneid="${booking["ZoneID"]}" value="${booking["ZoneID"]}">
                        <input type="hidden" class="user-date" data-usedate="${booking["Use Date"]}" value="${booking["Use Date"]}">
                        <input type="hidden" class="user-amount" data-amount="${booking["Amount"] || 0}" value="${booking["Amount"] || 0}">
                        <input type="hidden" class="user-orderid" data-orderid="${booking["Order No"]}" value="${booking["Order No"]}">
                        <input type="hidden" class="user-floor" data-floor="${booking["Floor"] || ''}" value="${booking["Floor"] || ''}">
                        <input type="hidden" class="user-table" data-table="${booking["Table"] || ''}" value="${booking["Table"] || ''}">
                        <div class="action-btn">
                            <a href="javascript:void(0)" class="text-primary edit">
                                <i class="ti ti-eye fs-5"></i>
                            </a>
                            <a href="javascript:void(0)" class="text-dark delete ms-2">
                                <i class="ti ti-trash fs-5"></i>
                            </a>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            } catch (error) {
                console.error('Error processing booking:', error, booking);
            }
        });
    } else {
        // No bookings available
        tableBody.innerHTML = '<tr><td colspan="11" class="text-center">No bookings found for this date.</td></tr>';
        console.log('No bookings available or data is not in expected format.');
    }
}


  // Function to format table data for better display
  function formatTableData(tableString) {
    if (!tableString) return '';

    // Remove curly braces if present
    let cleanedString = tableString.replace(/[{}]/g, '');

    // Split by comma
    let tables = cleanedString.split(',').map(t => t.trim()).filter(t => t);

    // If there's only one table, return it with a badge based on its floor and row
    if (tables.length === 1) {
      let table = tables[0];
      let colorClass = getColorForTable(table);
      return `<span class="badge bg-${colorClass} me-1">${table}</span>`;
    }

    // For large number of tables (e.g., entire floor booking)
    if (tables.length > 50) {
      // Count tables by row
      const rowCounts = {};

      tables.forEach(table => {
        if (!table) return;

        let row = '';

        // Handle newest format (e.g., A1/1, A2/2, etc.)
        if (table.includes('/')) {
          const parts = table.split('/');
          row = parts[0]; // A1, A2, etc.
        }
        // Handle previous format (e.g., 1A01, 2B03, etc.)
        else if (table.length >= 3 && /^\d/.test(table)) {
          row = table.charAt(1);
        }
        // Handle old format (e.g., A1, B2, etc.)
        else if (table.length >= 2) {
          row = table.charAt(0);
        }

        // Initialize the count if it doesn't exist
        if (!rowCounts[row]) {
          rowCounts[row] = 0;
        }

        // Increment the count
        rowCounts[row]++;
      });

      // Create a unique ID for this collapsible section
      const uniqueId = 'tableDetails_' + Math.random().toString(36).substr(2, 9);

      // Create a summary display
      let result = '<div class="table-groups">';
      result += `<div class="d-flex align-items-center">`;
      result += `<span class="badge bg-primary me-2">Total: ${tables.length} tables</span>`;
      result += `<button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#${uniqueId}" aria-expanded="false" aria-controls="${uniqueId}">Show Details</button>`;
      result += `</div>`;

      // Add a collapsible section for the details
      result += `<div class="collapse mt-2" id="${uniqueId}">`;

      // Sort rows by name
      const sortedRows = Object.keys(rowCounts).sort();

      // Display row counts
      sortedRows.forEach(row => {
        const colorClass = getColorForRow(row);
        let rowLabel = '';

        // For new format (A1, A2, etc.)
        if (row.startsWith('A')) {
          rowLabel = `Row ${row}`;
        }
        // For older formats
        else if (row === 'V') {
          rowLabel = 'VIP Room';
        } else if (row === 'P') {
          rowLabel = 'Floor 3 Premium';
        } else if (row === 'H') {
          rowLabel = 'Floor 2 Front';
        } else {
          rowLabel = `Row ${row}`;
        }

        result += `<div class="table-group mb-1">`;
        result += `<span class="badge bg-${colorClass} me-1">${rowLabel}: ${rowCounts[row]} tables</span>`;
        result += `</div>`;
      });

      // Add all tables in a scrollable container
      result += `<div class="table-details mt-2" style="max-height: 200px; overflow-y: auto;">`;

      // Group tables by their floor and row
      const groupedTables = {};

      tables.forEach(table => {
        if (!table) return;

        let row = '';

        // Handle newest format (e.g., A1/1, A2/2, etc.)
        if (table.includes('/')) {
          const parts = table.split('/');
          row = parts[0]; // A1, A2, etc.
        }
        // Handle previous format (e.g., 1A01, 2B03, etc.)
        else if (table.length >= 3 && /^\d/.test(table)) {
          row = table.charAt(1);
        }
        // Handle old format (e.g., A1, B2, etc.)
        else if (table.length >= 2) {
          row = table.charAt(0);
        }

        // Create a group key based on row
        const groupKey = row;

        // Initialize the group if it doesn't exist
        if (!groupedTables[groupKey]) {
          groupedTables[groupKey] = [];
        }

        // Add the table to its group
        groupedTables[groupKey].push(table);
      });

      // Sort groups by row
      const sortedGroups = Object.keys(groupedTables).sort();

      sortedGroups.forEach(groupKey => {
        const colorClass = getColorForRow(groupKey);

        // Create a row for this group with a label
        result += `<div class="table-group mb-1">`;

        // Add a small label for the row
        let rowLabel = '';

        // For new format (A1, A2, etc.)
        if (groupKey.startsWith('A')) {
          rowLabel = `Row ${groupKey}`;
        }
        // For older formats
        else if (groupKey === 'V') {
          rowLabel = 'VIP Room';
        } else if (groupKey === 'P') {
          rowLabel = 'Floor 3 Premium';
        } else if (groupKey === 'H') {
          rowLabel = 'Floor 2 Front';
        } else {
          rowLabel = `Row ${groupKey}`;
        }

        result += `<small class="text-muted me-2">${rowLabel}:</small>`;

        // Add each table in this group
        groupedTables[groupKey].forEach(table => {
          result += `<span class="badge bg-${colorClass} me-1">${table}</span>`;
        });

        result += '</div>';
      });

      result += `</div>`; // Close table-details
      result += `</div>`; // Close collapse
      result += '</div>'; // Close table-groups

      return result;
    }

    // For regular number of tables (original code)
    // Group tables by their floor and row
    const groupedTables = {};

    tables.forEach(table => {
      if (!table) return;

      // Get the floor and row from the table name
      let floor = '1'; // Default to floor 1
      let row = '';

      // Handle newest format (e.g., A1/1, A2/2, etc.)
      if (table.includes('/')) {
        const parts = table.split('/');
        row = parts[0]; // A1, A2, etc.
        // For this format, all tables are on floor 1
        floor = '1';
      }
      // Handle previous format (e.g., 1A01, 2B03, etc.)
      else if (table.length >= 3 && /^\d/.test(table)) {
        floor = table.charAt(0);
        row = table.charAt(1);
      }
      // Handle old format (e.g., A1, B2, etc.)
      else if (table.length >= 2) {
        row = table.charAt(0);
        // Determine floor based on row letter
        if (row === 'A' || row === 'B') floor = '1';
        else if (row === 'C' || row === 'D' || row === 'H') floor = '2';
        else if (row === 'E' || row === 'F' || row === 'G') floor = '3';
        else if (row === 'P') floor = '3'; // Premium tables on floor 3
        else if (row === 'V') floor = 'V'; // VIP rooms
      }

      // Create a group key based on floor and row
      const groupKey = row;

      // Initialize the group if it doesn't exist
      if (!groupedTables[groupKey]) {
        groupedTables[groupKey] = [];
      }

      // Add the table to its group
      groupedTables[groupKey].push(table);
    });

    // Create HTML for each group
    let result = '<div class="table-groups">';

    // Sort groups by row
    const sortedGroups = Object.keys(groupedTables).sort();

    sortedGroups.forEach(groupKey => {
      const colorClass = getColorForRow(groupKey);

      // Create a row for this group with a label
      result += `<div class="table-group mb-1">`;

      // Add a small label for the row
      let rowLabel = '';

      // For new format (A1, A2, etc.)
      if (groupKey.startsWith('A')) {
        const rowNumber = groupKey.substring(1);
        rowLabel = `Row ${groupKey}`;
      }
      // For older formats
      else if (groupKey === 'V') {
        rowLabel = 'VIP Room';
      } else if (groupKey === 'P') {
        rowLabel = 'Floor 3 Premium';
      } else if (groupKey === 'H') {
        rowLabel = 'Floor 2 Front';
      } else {
        rowLabel = `Row ${groupKey}`;
      }

      result += `<small class="text-muted me-2">${rowLabel}:</small>`;

      // Add each table in this group
      groupedTables[groupKey].forEach(table => {
        result += `<span class="badge bg-${colorClass} me-1">${table}</span>`;
      });

      result += '</div>';
    });

    result += '</div>';
    return result;
  }

  // Helper function to determine color based on row letter or row identifier
  function getColorForRow(row) {
    // For new format (e.g., A1, A2, etc.)
    if (row.startsWith('A')) {
      const rowNumber = parseInt(row.substring(1), 10);

      // Map row numbers to colors
      switch(rowNumber) {
        case 1: return 'success';  // Green
        case 2: return 'warning';  // Yellow
        case 3: return 'danger';   // Red
        case 4: return 'info';     // Light Blue
        case 5: return 'primary';  // Blue
        case 6: return 'secondary'; // Gray
        case 7: return 'dark';     // Dark
        case 8: return 'purple';   // Purple
        default: return 'primary'; // Default to blue
      }
    }

    // For older formats, use the original color mapping
    const colorMap = {
      'A': 'success',  // Green
      'B': 'warning',  // Yellow
      'C': 'danger',   // Red
      'D': 'info',     // Light Blue
      'E': 'primary',  // Blue
      'F': 'secondary', // Gray
      'G': 'dark',     // Dark
      'H': 'purple',   // Purple for front zone
      'P': 'purple',   // Purple for premium tables
      'V': 'pink'      // Pink for VIP rooms
    };

    // Return the mapped color or a default
    return colorMap[row] || 'primary';
  }

  // Helper function to determine color based on full table name
  function getColorForTable(table) {
    // For newest format (e.g., A1/1, A2/2, etc.)
    if (table.includes('/')) {
      const parts = table.split('/');
      const row = parts[0]; // A1, A2, etc.
      return getColorForRow(row);
    }
    // For previous format (e.g., 1A01, 2B03)
    else if (table.length >= 3 && /^\d/.test(table)) {
      const row = table.charAt(1).toUpperCase();
      return getColorForRow(row);
    }
    // For old format (e.g., A1, B2)
    else if (table.length >= 2) {
      const row = table.charAt(0).toUpperCase();
      return getColorForRow(row);
    }

    return 'primary'; // Default color
  }

  // Add event listener for date changes
  document.getElementById('myDate').addEventListener('change', formatAndDisplayDate);

  // Call the function on page load
  // Initialize when DOM is fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Set the date input to today's date
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();

    // Set the date input value
    document.getElementById('myDate').value = `${year}-${month}-${day}`;

    // Format and display the date (this will also fetch bookings)
    formatAndDisplayDate();

    // Get the formatted date for API calls
    const formattedDate = `${day}-${month}-${year}`;

    // Fetch bookings for today
    console.log('Initial load - fetching bookings for today:', formattedDate);
    fetchBookingsByDate(formattedDate);
    getZoneJson(formattedDate);

    // Initialize tooltips for dynamically added elements
    document.addEventListener('mouseover', function(e) {
      // Check if the element or its parent has a badge class
      const target = e.target.closest('.badge');
      if (target && !target.hasAttribute('data-bs-toggle')) {
        // Get the table name
        const tableName = target.textContent.trim();

        // Create descriptive tooltip
        let tooltipText = 'Table: ' + tableName;

        // For newest format (e.g., A1/1, A2/2, etc.)
        if (tableName.includes('/')) {
          const parts = tableName.split('/');
          const row = parts[0]; // A1, A2, etc.
          const tableNumber = parts[1];

          if (row.startsWith('A')) {
            const rowNumber = row.substring(1);
            tooltipText += ` - Floor 1, Row ${row}, Table ${tableNumber}`;
          }
        }
        // For previous format (e.g., 1A01, 2B03)
        else if (tableName.length >= 3 && /^\d/.test(tableName)) {
          const floor = tableName.charAt(0);
          const row = tableName.charAt(1).toUpperCase();

          if (row === 'H') {
            tooltipText += ' - Floor 2 Front Zone';
          } else if (row === 'P') {
            tooltipText += ' - Floor 3 Premium Table';
          } else {
            tooltipText += ` - Floor ${floor}, Row ${row}`;
          }
        }
        // For old format (e.g., A1, B2)
        else if (tableName.length >= 2) {
          const prefix = tableName.charAt(0).toUpperCase();

          // Add additional information based on prefix
          switch(prefix) {
            case 'A':
              tooltipText += ' - Floor 1, Row A';
              break;
            case 'B':
              tooltipText += ' - Floor 1, Row B';
              break;
            case 'C':
              tooltipText += ' - Floor 2, Row A';
              break;
            case 'D':
              tooltipText += ' - Floor 2, Row B';
              break;
            case 'E':
              tooltipText += ' - Floor 3, Row A';
              break;
            case 'F':
              tooltipText += ' - Floor 3, Row B';
              break;
            case 'G':
              tooltipText += ' - Floor 3, Row C';
              break;
            case 'H':
              tooltipText += ' - Floor 2, Front Zone';
              break;
            case 'P':
              tooltipText += ' - Floor 3, Premium Table';
              break;
            case 'V':
              tooltipText += ' - VIP Room';
              break;
          }
        }

        // Add tooltip attributes
        target.setAttribute('data-bs-toggle', 'tooltip');
        target.setAttribute('data-bs-placement', 'top');
        target.setAttribute('data-bs-title', tooltipText);

        // Initialize the tooltip
        new bootstrap.Tooltip(target);
      }
    });
  });
</script>
