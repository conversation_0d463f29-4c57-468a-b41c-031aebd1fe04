# JSON File Auto-Load System

This system automatically creates JSON files for the booking system up to a specified date.

## Files

1. **CreateFileByDate.php** - Main script that creates JSON files for a date range
2. **autoload_files.php** - Script that automatically creates files up to the current date
3. **autoload.html** - Web interface for manually triggering the auto-load process
4. **cron_autoload.php** - Script that can be run via cron job or scheduled task
5. **startup_autoload.bat** - Windows batch file that can be added to startup

## Usage

### Web Interface

1. Open `autoload.html` in a web browser
2. The system will automatically create JSON files up to the current date
3. You can also specify a different date and manually trigger the process

### Command Line

To create files up to a specific date:
```
php CreateFileByDate.php?upTodate=2025-04-10
```

### Automatic Startup (Windows)

1. Create a shortcut to `startup_autoload.bat`
2. Place the shortcut in your Windows Startup folder:
   `C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup`

### Cron Job (Linux/Unix)

Add the following line to your crontab to run daily at midnight:
```
0 0 * * * php /path/to/cron_autoload.php
```

## Logs

The cron job logs its activity to `autoload_log.txt` in the same directory.

## Troubleshooting

If you encounter issues:

1. Check the `autoload_log.txt` file for error messages
2. Ensure the web server has write permissions to the `json` directory
3. Verify that the database connection is working properly
