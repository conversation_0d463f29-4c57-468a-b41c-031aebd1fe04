<div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Sidebar -->
        <!-- ---------------------------------- -->
        <div class="iconbar">
            <div>
                
                <?php
                    include_once("_menuMain.php");
                    include_once("_menuPages.php");
                ?>
                                
            </div>
        </div>
    </aside>
    <!--  Sidebar End -->


    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <div class="row">

                    <div class="col-lg-12">
                        <!-- -------------------------------------------- -->
                        <!-- Revenue by Product -->
                        <!-- -------------------------------------------- -->
                        <div class="card">
                            <div class="card-body">
                                <!-- <div class="d-flex flex-wrap gap-3 mb-2 justify-content-between align-items-center">
                                    <h5 class="card-title fw-semibold mb-0">Dinner</h5>
                                </div> -->

                                <div class="table-responsive mb-3">
                                    <ul class="nav nav-tabs theme-tab gap-3 flex-nowrap" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <a class="nav-link active" data-bs-toggle="tab" href="#app" role="tab" aria-selected="true">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:widget-linear" class="fs-4"></iconify-icon>
                                                <span>Dinner</span>
                                            </div>

                                            </a>
                                        </li>
                                        <!-- <li class="nav-item" role="presentation">
                                            <a class="nav-link" data-bs-toggle="tab" href="#mobile" role="tab" aria-selected="false" tabindex="-1">
                                            <div class="hstack gap-2">
                                                <iconify-icon icon="solar:smartphone-line-duotone" class="fs-4"></iconify-icon>
                                                <span>Sunset</span>
                                            </div>
                                            </a>
                                        </li>                                         -->
                                    </ul>
                                </div>

                                <div class="tab-content mb-n3">
                                    <div class="tab-pane active" id="app" role="tabpanel">
                                        <div class="table-responsive">  
                                            <table class="table table-sm mb-0 table-hover">
                                            <thead>
                                                <tr>
                                                    <th scope="col" rowspan="2" class="text-center bg-danger bg-opacity-25" style="vertical-align: middle;font-size: larger;">Date</th>
                                                    <th scope="col" colspan="2" class="text-center bg-warning bg-opacity-75">Rooftop</th>
                                                    <th scope="col" colspan="2" class="text-center bg-info bg-opacity-75">Prow</th>
                                                    <th scope="col" colspan="2" class="text-center bg-warning bg-opacity-75">Fl.2</th>
                                                    <th scope="col" colspan="2" class="text-center bg-info bg-opacity-75">Fl.1</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-50" style="vertical-align: middle;font-size: larger;">VIP1</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-25" style="vertical-align: middle;font-size: larger;">VIP2</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-50" style="vertical-align: middle;font-size: larger;">VIP3</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-25" style="vertical-align: middle;font-size: larger;">VIP4</th>
                                                </tr>
                                                <tr>
                                                    <th scope="col" class="text-center bg-warning bg-opacity-25">Edge</th>
                                                    <th scope="col" class="text-center bg-warning bg-opacity-10">Middle</th>
                                                    <th scope="col" class="text-center bg-info bg-opacity-25">Edge</th>
                                                    <th scope="col" class="text-center bg-info bg-opacity-10">Middle</th>
                                                    <th scope="col" class="text-center bg-warning bg-opacity-25">Edge</th>
                                                    <th scope="col" class="text-center bg-warning bg-opacity-10">Middle</th>
                                                    <th scope="col" class="text-center bg-info bg-opacity-25">Edge</th>
                                                    <th scope="col" class="text-center bg-info bg-opacity-10">Middle</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-25">Fl.2</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-10">Fl.2</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-25">Fl.1</th>
                                                    <th scope="col" class="text-center bg-success bg-opacity-10">Fl.1</th>


                                                </tr>
                                            </thead>
                                            <tbody id="table">
                                                <?php
                                                // ตั้งค่าโซนเวลา เปลี่ยน 'Asia/Bangkok' ตามที่ต้องการ
                                                date_default_timezone_set('Asia/Bangkok');

                                                // รับวันแรกของเดือน
                                                $first_day_of_month = date('Y-m-01');
                                                // รับวันสุดท้ายของเดือน
                                                $last_day_of_month = date('Y-m-t');
                                                // รับวันที่ปัจจุบัน
                                                $today = date('Y-m-d');

                                                // วนลูปตั้งแต่วันที่ 1 ถึงวันสุดท้ายของเดือน
                                                $current_date = $first_day_of_month;
                                                while ($current_date <= $last_day_of_month) {
                                                    $p_roof = rand(1, 40);
                                                    $p_floor1 = rand(1, 100);
                                                    $p_floor2 = rand(1, 100);
                                                    $p_floor3 = rand(1, 100);

                                                    $bar_color_roof = 'bg-primary';
                                                    $bar_color_floor1 = 'bg-primary';
                                                    $bar_color_floor2 = 'bg-primary';
                                                    $bar_color_floor3 = 'bg-primary';

                                                    if ($p_roof < 50) {
                                                        $bar_color_roof = 'bg-danger';
                                                    } elseif ($p_roof < 80) {
                                                        $bar_color_roof = 'bg-warning';
                                                    } else {
                                                        $bar_color_roof = 'bg-success';
                                                    }

                                                    if ($p_floor1 < 50) {
                                                        $bar_color_floor1 = 'bg-danger';
                                                    } elseif ($p_floor1 < 80) {
                                                        $bar_color_floor1 = 'bg-warning';
                                                    } else {
                                                        $bar_color_floor1 = 'bg-success';
                                                    }

                                                    if ($p_floor2 < 50) {
                                                        $bar_color_floor2 = 'bg-danger';
                                                    } elseif ($p_floor2 < 80) {
                                                        $bar_color_floor2 = 'bg-warning';
                                                    } else {
                                                        $bar_color_floor2 = 'bg-success';
                                                    }

                                                    if ($p_floor3 < 50) {
                                                        $bar_color_floor3 = 'bg-danger';
                                                    } elseif ($p_floor3 < 80) {
                                                        $bar_color_floor3 = 'bg-warning';
                                                    } else {
                                                        $bar_color_floor3 = 'bg-success';
                                                    }

                                                    // เน้นวันที่ปัจจุบัน
                                                    $highlight_class = ($current_date == $today) ? 'table-danger  ' : '';
                                                    
                                                    echo '
                                                    <tr  class="'.$highlight_class.'">
                                                        <td class="text-center bg-danger bg-opacity-10">                                                            
                                                            <strong>'.date('d', strtotime($current_date)).'</strong><small class="text-muted">/'.date('m/Y', strtotime($current_date)).'</small><br/>
                                                            '.date('l', strtotime($current_date)).'
                                                        </td>
                                                        <td class="text-center bg-warning bg-opacity-10">
                                                            <strong>'.$p_roof.'</strong>/40<br/>
                                                            <span class="badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/200</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><span class="m-2 badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/180</span></div>
                                                        </td>
                                                        <td class="text-center bg-info bg-opacity-10">
                                                            <strong>'.$p_roof.'</strong>/40<br/>
                                                            <span class="badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/200</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><span class="m-2 badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/180</span></div>
                                                        </td>
                                                        <td class="text-center bg-warning bg-opacity-10">
                                                            <strong>'.$p_roof.'</strong>/40<br/>
                                                            <span class="badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/200</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><span class="m-2 badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/180</span></div>
                                                        </td>
                                                        <td class="text-center bg-info bg-opacity-10">
                                                            <strong>'.$p_roof.'</strong>/40<br/>
                                                            <span class="badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/200</span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><span class="m-2 badge bg-danger-subtle text-danger"><strong>'.$p_roof.'</strong>/180</span></div>
                                                        </td>
                                                        <td class="text-center bg-success bg-opacity-10">
                                                            <div class="mt-2"><i class="ti ti-check h4" style="color:green"></i></div>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><i class="ti ti-square-rounded-x h4" style="color:red"></i></div>
                                                        </td>
                                                        <td class="text-center bg-success bg-opacity-10">
                                                            <div class="mt-2"><i class="ti ti-check h4" style="color:green"></i></div>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="mt-2"><i class="ti ti-check h4" style="color:green"></i></div>
                                                        </td>
                                                    </tr>';
                                                    $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
                                                }                                                
                                                ?>
                                            </tbody>
                                            </table>
                                        </div>
                                        
                                    </div>                                    
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!-- <button
            class="btn btn-danger p-3 rounded-circle d-flex align-items-center justify-content-center customizer-btn"
            type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample"
            aria-controls="offcanvasExample">
            <i class="icon ti ti-settings fs-7"></i>
        </button> -->

        <div class="offcanvas customizer offcanvas-end" tabindex="-1" id="offcanvasExample"
            aria-labelledby="offcanvasExampleLabel">
            <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                <h4 class="offcanvas-title fw-semibold" id="offcanvasExampleLabel">
                    Settings
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body" data-simplebar style="height: calc(100vh - 80px)">
                <h6 class="fw-semibold fs-4 mb-2">Theme</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check light-layout" name="theme-layout" id="light-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="light-layout">
                        <i class="icon ti ti-brightness-up fs-7 me-2"></i>Light
                    </label>

                    <input type="radio" class="btn-check dark-layout" name="theme-layout" id="dark-layout"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="dark-layout">
                        <i class="icon ti ti-moon fs-7 me-2"></i>Dark
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Direction</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="direction-l" id="ltr-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="ltr-layout">
                        <i class="icon ti ti-text-direction-ltr fs-7 me-2"></i>LTR
                    </label>

                    <input type="radio" class="btn-check" name="direction-l" id="rtl-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="rtl-layout">
                        <i class="icon ti ti-text-direction-rtl fs-7 me-2"></i>RTL
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Theme Colors</h6>

                <div class="d-flex flex-row flex-wrap gap-3 customizer-box color-pallete" role="group">
                    <input type="radio" class="btn-check" name="color-theme-layout" id="Blue_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Blue_Theme')" for="Blue_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="BLUE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-1">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Aqua_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Aqua_Theme')" for="Aqua_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="AQUA_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-2">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="Purple_Theme"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Purple_Theme')" for="Purple_Theme" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="PURPLE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-3">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="green-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Green_Theme')" for="green-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="GREEN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-4">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="cyan-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Cyan_Theme')" for="cyan-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="CYAN_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-5">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>

                    <input type="radio" class="btn-check" name="color-theme-layout" id="orange-theme-layout"
                        autocomplete="off" />
                    <label
                        class="btn p-9 btn-outline-primary rounded-2 d-flex align-items-center justify-content-center"
                        onclick="handleColorTheme('Orange_Theme')" for="orange-theme-layout" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="ORANGE_THEME">
                        <div class="color-box rounded-circle d-flex align-items-center justify-content-center skin-6">
                            <i class="ti ti-check text-white d-flex icon fs-5"></i>
                        </div>
                    </label>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Layout Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="vertical-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="vertical-layout">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Vertical
                        </label>
                    </div>
                    <div>
                        <input type="radio" class="btn-check" name="page-layout" id="horizontal-layout"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="horizontal-layout">
                            <i class="icon ti ti-layout-navbar fs-7 me-2"></i>Horizontal
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Container Option</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="layout" id="boxed-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="boxed-layout">
                        <i class="icon ti ti-layout-distribute-vertical fs-7 me-2"></i>Boxed
                    </label>

                    <input type="radio" class="btn-check" name="layout" id="full-layout" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="full-layout">
                        <i class="icon ti ti-layout-distribute-horizontal fs-7 me-2"></i>Full
                    </label>
                </div>

                <h6 class="fw-semibold fs-4 mb-2 mt-5">Sidebar Type</h6>
                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <a href="javascript:void(0)" class="fullsidebar">
                        <input type="radio" class="btn-check" name="sidebar-type" id="full-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="full-sidebar">
                            <i class="icon ti ti-layout-sidebar-right fs-7 me-2"></i>Full
                        </label>
                    </a>
                    <div>
                        <input type="radio" class="btn-check" name="sidebar-type" id="mini-sidebar"
                            autocomplete="off" />
                        <label class="btn p-9 btn-outline-primary rounded-2" for="mini-sidebar">
                            <i class="icon ti ti-layout-sidebar fs-7 me-2"></i>Collapse
                        </label>
                    </div>
                </div>

                <h6 class="mt-5 fw-semibold fs-4 mb-2">Card With</h6>

                <div class="d-flex flex-row gap-3 customizer-box" role="group">
                    <input type="radio" class="btn-check" name="card-layout" id="card-with-border" autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-with-border">
                        <i class="icon ti ti-border-outer fs-7 me-2"></i>Border
                    </label>

                    <input type="radio" class="btn-check" name="card-layout" id="card-without-border"
                        autocomplete="off" />
                    <label class="btn p-9 btn-outline-primary rounded-2" for="card-without-border">
                        <i class="icon ti ti-border-none fs-7 me-2"></i>Shadow
                    </label>
                </div>
            </div>
        </div>

        <script>
            function handleColorTheme(e) {
                document.documentElement.setAttribute("data-color-theme", e);
            }

        </script>
    </div>


</div>