<?php
/**
 * Auto-load JSON files for the booking system
 * This script automatically creates JSON files for bookings up to the current date
 */

// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
include '../dbconnect/_dbconnect.php';

// Get current date
$currentDate = date('Y-m-d');

// Log the start of the process
error_log("Starting auto-load process for JSON files up to $currentDate");

// Create a function to call the CreateFileByDate.php script
function createFilesUpToDate($date) {
    $url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/CreateFileByDate.php?upTodate=$date";
    
    // Use cURL to make the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // Check if the request was successful
    if ($httpCode == 200) {
        $result = json_decode($response, true);
        if (isset($result['status']) && $result['status'] === 'success') {
            error_log("Successfully created JSON files up to $date");
            return true;
        } else {
            error_log("Error creating JSON files: " . json_encode($result));
            return false;
        }
    } else {
        error_log("HTTP error $httpCode when calling CreateFileByDate.php");
        return false;
    }
}

// Call the function to create files up to the current date
$result = createFilesUpToDate($currentDate);

// Output the result
if ($result) {
    echo json_encode([
        'status' => 'success',
        'message' => "JSON files created successfully up to $currentDate"
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => "Failed to create JSON files"
    ]);
}
