/**
 * Table Styles for the new naming convention
 */

/* Floor 1 tables  */
[id^="box-C1"] rect,
[id^="rbox-C1"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C2"] rect,
[id^="rbox-C2"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C3"] rect,
[id^="rbox-C3"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C4"] rect,
[id^="rbox-C4"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C5"] rect,
[id^="rbox-C5"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C6"] rect,
[id^="rbox-C6"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C7"] rect,
[id^="rbox-C7"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C8"] rect,
[id^="rbox-C8"] {
    fill: #dc3545 !important;
    /* Red */
}

[id^="box-C9"] rect,
[id^="rbox-C9"] {
    fill: #dc3545 !important;
    /* Red */
}





/* Floor 2 tables */
[id^="box-H1"] rect,
[id^="rbox-H1"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H2"] rect,
[id^="rbox-H2"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H3"] rect,
[id^="rbox-H3"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H4"] rect,
[id^="rbox-H4"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H5"] rect,
[id^="rbox-H5"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H6"] rect,
[id^="rbox-H6"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H7"] rect,
[id^="rbox-H7"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H8"] rect,
[id^="rbox-H8"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-H9"] rect,
[id^="rbox-H9"] {
    fill: #6f42c1 !important;
    /* Purple */
}

[id^="box-B1"] rect,
[id^="rbox-B1"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B2"] rect,
[id^="rbox-B2"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B3"] rect,
[id^="rbox-B3"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B4"] rect,
[id^="rbox-B4"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B5"] rect,
[id^="rbox-B5"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B6"] rect,
[id^="rbox-B6"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B7"] rect,
[id^="rbox-B7"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B8"] rect,
[id^="rbox-B8"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-B9"] rect,
[id^="rbox-B9"] {
    fill: #ffc107 !important;
    /* Yellow */
}

[id^="box-V"] rect,
[id^="rbox-V"] {
    fill: #12deb9 !important;
    /* Yellow */
}



/* Floor 3 tables (3A01, 3B01, 3C01, etc.) */
[id^="box-3A"] rect,
[id^="rbox-3A"] {
    fill: #28a745 !important;
    /* Green */
}


[id^="box-3C"] rect,
[id^="rbox-3C"] {
    fill: #dc3545 !important;
    /* Red */
}

/* Premium tables (3P01, 3P02, etc.) */
[id^="box-3P"] rect,
[id^="rbox-3P"] {
    fill: #6f42c1 !important;
    /* Purple */
}

/* Selected tables */
[data-value="1"] rect {
    fill: #00FF00 !important;
    /* Bright Green */
}

/* Booked tables */
.booked-disabled rect {
    fill: #cccccc !important;
    /* Gray */
}

/* Disabled tables */
.table-disabled rect {
    fill: #999999 !important;
    /* Dark Gray */
    cursor: not-allowed;
    opacity: 0.7;
    stroke-dasharray: 5, 5;
    stroke: #666666;
    stroke-width: 2;
}

/* Ensure text is visible on disabled tables */
.table-disabled text {
    fill: #ffffff !important;
    font-weight: bold;
}

/* Tooltip for disabled tables */
.table-tooltip {
    position: absolute;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    white-space: nowrap;
    display: none;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Add arrow to tooltip */
.table-tooltip:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid rgba(0, 0, 0, 0.8);
}

/* Hover effect for tables */
.svg-box:not(.booked-disabled):hover rect {
    opacity: 0.8;
    stroke-width: 3px;
    transition: all 0.2s ease;
}

/* Table text styling */
.svg-box text {
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    fill: #ffffff;
    text-anchor: middle;
    dominant-baseline: middle;
}

/* Make sure text is always visible */
.svg-box:hover text {
    fill: #ffffff !important;
    font-weight: bold;
}