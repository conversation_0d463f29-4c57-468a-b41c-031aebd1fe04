<?php
// Include database connection
include 'dbconnect/_dbconnect.php';

// Connect to database
$conn = db_connect();

// Get all tables
$sql = "SHOW TABLES";
$result = $conn->query($sql);

// Display tables
echo "<h2>Database Tables</h2>";
echo "<ul>";
while ($row = $result->fetch(PDO::FETCH_NUM)) {
    echo "<li>" . $row[0] . "</li>";
}
echo "</ul>";

// Check if there's an agents table
$sql = "SHOW TABLES LIKE 'kp_agent%'";
$result = $conn->query($sql);
$num = $result->rowCount();

echo "<h2>Agent Tables</h2>";
if ($num > 0) {
    echo "<ul>";
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No agent tables found.</p>";
}

// Close connection
db_close($conn);
?>
