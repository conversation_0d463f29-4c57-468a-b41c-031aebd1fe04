<?php
/**
 * Security Class
 *
 * Provides security-related utility functions
 */
class Security {
    /**
     * Sanitize input data
     *
     * @param string $data Input data
     * @return string Sanitized data
     */
    public static function sanitizeInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        return $data;
    }

    /**
     * Sanitize an array of input data
     *
     * @param array $data Input data array
     * @return array Sanitized data array
     */
    public static function sanitizeArray($data) {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = self::sanitizeArray($value);
            } else {
                $sanitized[$key] = self::sanitizeInput($value);
            }
        }

        return $sanitized;
    }

    /**
     * Validate email address
     *
     * @param string $email Email address
     * @return bool True if valid, false otherwise
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate date format
     *
     * @param string $date Date string
     * @param string $format Date format
     * @return bool True if valid, false otherwise
     */
    public static function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * Generate a random token
     *
     * @param int $length Token length
     * @return string Random token
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Hash a password
     *
     * @param string $password Plain text password
     * @return string Hashed password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }

    /**
     * Verify a password against a hash
     *
     * @param string $password Plain text password
     * @param string $hash Password hash
     * @return bool True if valid, false otherwise
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * Check if a password hash needs to be rehashed
     *
     * @param string $hash Password hash
     * @return bool True if needs rehashing, false otherwise
     */
    public static function passwordNeedsRehash($hash) {
        return password_needs_rehash($hash, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }

    /**
     * Prevent XSS attacks by setting appropriate headers
     */
    public static function setSecurityHeaders() {
        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' https://fonts.gstatic.com; connect-src 'self';");

        // X-Content-Type-Options
        header("X-Content-Type-Options: nosniff");

        // X-Frame-Options
        header("X-Frame-Options: SAMEORIGIN");

        // X-XSS-Protection
        header("X-XSS-Protection: 1; mode=block");

        // Referrer-Policy
        header("Referrer-Policy: strict-origin-when-cross-origin");

        // Feature-Policy
        header("Feature-Policy: camera 'none'; microphone 'none'; geolocation 'none'");
    }

    /**
     * Rate limit function to prevent brute force attacks
     *
     * @param string $key Unique key for rate limiting (e.g., IP address, username)
     * @param int $maxAttempts Maximum number of attempts
     * @param int $timeWindow Time window in seconds
     * @return bool True if rate limit not exceeded, false otherwise
     */
    public static function checkRateLimit($key, $maxAttempts = 5, $timeWindow = 300) {
        $cacheDir = dirname(__DIR__) . '/cache';
        $cacheFile = $cacheDir . '/rate_limit_' . md5($key) . '.json';

        // Create cache directory if it doesn't exist
        if (!file_exists($cacheDir)) {
            if (!@mkdir($cacheDir, 0777, true)) {
                // If directory creation fails, log the error but continue
                error_log("Failed to create cache directory: {$cacheDir}");
                // Return true to allow the request to proceed
                return true;
            }
        }

        // Check if the directory is writable
        if (!is_writable($cacheDir)) {
            error_log("Cache directory is not writable: {$cacheDir}");
            // Return true to allow the request to proceed
            return true;
        }

        // Get current attempts
        $attempts = [];

        if (file_exists($cacheFile)) {
            $fileContent = @file_get_contents($cacheFile);
            if ($fileContent !== false) {
                $decoded = json_decode($fileContent, true);
                if ($decoded !== null) {
                    $attempts = $decoded;
                }
            }
        }

        // Remove attempts outside the time window
        $now = time();
        $attempts = array_filter($attempts, function($timestamp) use ($now, $timeWindow) {
            return $timestamp > ($now - $timeWindow);
        });

        // Check if rate limit exceeded
        if (count($attempts) >= $maxAttempts) {
            return false;
        }

        // Add current attempt
        $attempts[] = $now;

        // Save attempts
        if (@file_put_contents($cacheFile, json_encode($attempts)) === false) {
            // If saving fails, log the error but continue
            error_log("Failed to write to cache file: {$cacheFile}");
        }

        return true;
    }
}
