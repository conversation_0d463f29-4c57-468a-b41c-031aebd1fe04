<?php
/**
 * Test script to verify JSON file generation when a booking is saved
 */

// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
include_once('../dbconnect/_dbconnect.php');

// Set content type to JSON
header('Content-Type: application/json');

// Create a test booking
$name = 'Test Customer';
$phone = '0123456789';
$adult = 2;
$child = 1;
$infant = 0;
$guide = 0;
$foc = 0;
$tl = 0;
$useDate = date('Y-m-d'); // Today
$useZone = '1';
$voucher = 'TEST-VOUCHER';
$agent = 'Test Agent';
$remark = 'Test booking for JSON generation';
$cruiseId = 1;
$zoneId = 1;
$tables = 'A1/1,A1/2';
$amount = 1000;
$userKey = 'TEST-KEY-001';

try {
    // Insert the test booking
    $orderNo = insert_booking(
        $name,
        $phone,
        $adult,
        $child,
        $infant,
        $guide,
        $foc,
        $tl,
        $useDate,
        $useZone,
        $voucher,
        $agent,
        $remark,
        $cruiseId,
        $zoneId,
        $tables,
        $amount,
        $userKey
    );
    
    // Check if JSON files were created
    $month = date('m', strtotime($useDate));
    $year = date('Y', strtotime($useDate));
    $day = date('d', strtotime($useDate));
    $folder = 'json/' . $year . '/' . $month;
    $bookingJsonFile = $folder . '/jsonBookingBy_' . $day . '.json';
    $zoneJsonFile = $folder . '/json_by_zone.json';
    
    $bookingJsonExists = file_exists($bookingJsonFile);
    $zoneJsonExists = file_exists($zoneJsonFile);
    
    // Return results
    echo json_encode([
        'status' => 'success',
        'message' => 'Test booking created with order number: ' . $orderNo,
        'booking_json_file' => $bookingJsonFile,
        'booking_json_exists' => $bookingJsonExists,
        'zone_json_file' => $zoneJsonFile,
        'zone_json_exists' => $zoneJsonExists,
        'date' => $useDate
    ]);
    
} catch (Exception $e) {
    // Return error
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
