<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

include '../dbconnect/_dbconnect.php';

try {
    // Validate input
    if (!isset($_POST['tables'])) {
        throw new Exception('Missing tables data');
    }
    
    $tables = json_decode($_POST['tables'], true);
    if (!is_array($tables) || empty($tables)) {
        throw new Exception('Invalid tables data');
    }
    
    // Connect to database
    $conn = db_connect();
    
    // Begin transaction
    $conn->beginTransaction();
    
    // Prepare insert statement
    $sql = "INSERT INTO kp_tables (table_id, x_position, y_position, width, height, status) 
            VALUES (:id, :x, :y, :width, :height, :status)
            ON DUPLICATE KEY UPDATE 
            x_position = :x, 
            y_position = :y, 
            width = :width, 
            height = :height, 
            status = :status";
    
    $stmt = $conn->prepare($sql);
    
    // Insert each table
    foreach ($tables as $table) {
        // Validate table data
        if (!isset($table['id']) || !isset($table['x']) || !isset($table['y']) || 
            !isset($table['width']) || !isset($table['height']) || !isset($table['status'])) {
            throw new Exception('Invalid table data structure');
        }
        
        // Bind parameters
        $stmt->bindParam(':id', $table['id']);
        $stmt->bindParam(':x', $table['x']);
        $stmt->bindParam(':y', $table['y']);
        $stmt->bindParam(':width', $table['width']);
        $stmt->bindParam(':height', $table['height']);
        $stmt->bindParam(':status', $table['status']);
        
        // Execute statement
        $stmt->execute();
    }
    
    // Commit transaction
    $conn->commit();
    
    // Return success
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
