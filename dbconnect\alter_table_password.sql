-- Alter the kp_login table to increase the size of the pass column
ALTER TABLE `kp_login` 
MODIFY COLUMN `pass` VARCHAR(255) DEFAULT NULL;

-- Add a comment to explain the change
ALTER TABLE `kp_login` 
<PERSON><PERSON>IFY COLUMN `pass` VARCHAR(255) DEFAULT NULL COMMENT 'Stores password hash (Argon2id)';

-- Optional: Add an email column if it doesn't exist
-- Check if the email column exists
SET @exists = 0;
SELECT COUNT(*) INTO @exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'kp_login' AND column_name = 'email';

-- Add the email column if it doesn't exist
SET @query = IF(@exists = 0, 
    'ALTER TABLE `kp_login` ADD COLUMN `email` VARCHAR(100) DEFAULT NULL AFTER `name`', 
    'SELECT "Email column already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
