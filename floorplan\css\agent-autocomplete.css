/**
 * Styles for agent autocomplete fields
 */

/* Style for the agent autocomplete input */
.agent-autocomplete {
    background-color: #f8f9fa;
    border-color: #ced4da;
    transition: all 0.3s ease;
}

/* Focus effect */
.agent-autocomplete:focus {
    background-color: #fff;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Hover effect */
.agent-autocomplete:hover {
    background-color: #e9ecef;
}

/* Style for datalist options (only visible in some browsers) */
option {
    padding: 8px;
    font-size: 14px;
}

/* Custom styling for the input group */
.input-group:has(.agent-autocomplete) {
    margin-bottom: 0;
}
