<?php
// Include database connection
include '../dbconnect/_dbconnect.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

try {
    // Connect to database
    $conn = db_connect();
    
    // Check if the kp_agent table exists
    $checkTableSql = "SHOW TABLES LIKE 'kp_agent'";
    $checkTableStmt = $conn->prepare($checkTableSql);
    $checkTableStmt->execute();
    
    if ($checkTableStmt->rowCount() === 0) {
        // Table doesn't exist, create it
        $createTableSql = "CREATE TABLE kp_agent (
            id INT(11) NOT NULL AUTO_INCREMENT,
            name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
            contact_person VARCHAR(255),
            phone VARCHAR(20),
            email VARCHAR(255),
            address TEXT,
            status TINYINT(1) DEFAULT 1,
            create_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            update_date DATETIME ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        
        $conn->exec($createTableSql);
        
        // Insert some default agents
        $insertSql = "INSERT INTO kp_agent (name, status) VALUES 
            ('Agent 1', 1),
            ('Agent 2', 1),
            ('Agent 3', 1),
            ('Agent 4', 1),
            ('Agent 5', 1),
            ('Bangkok Tour', 1),
            ('Phuket Tour', 1),
            ('Chiang Mai Tour', 1),
            ('Pattaya Tour', 1),
            ('Krabi Tour', 1)";
        
        $conn->exec($insertSql);
    }
    
    // Get all active agents
    $sql = "SELECT id, name FROM kp_agent WHERE status = 1 ORDER BY name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return the agents as JSON
    echo json_encode(['success' => true, 'data' => $agents]);
} catch (PDOException $e) {
    // Return error message
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
} finally {
    // Close database connection
    if (isset($conn)) {
        db_close($conn);
    }
}
?>
