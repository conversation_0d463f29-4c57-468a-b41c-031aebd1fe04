<?php
session_start();
// include dbconnect
include_once("../dbconnect/_dbconnect.php");

date_default_timezone_set('Asia/Bangkok');

// รับวันแรกของเดือน
$first_day_of_month = date('Y-m-01');
// รับวันสุดท้ายของเดือน
$last_day_of_month = date('Y-m-t');
// รับวันที่ปัจจุบัน
$today = date('Y-m-d');

// // get data from database
// // print_r($_SESSION["cruiseId"]);
// $cruiseId = json_decode($_SESSION["cruiseId"], true)["cruise_id"];
// echo $cruiseId;
// // echo get_zone_by_cruise_id($cruiseId);
// $zones = json_decode(get_zone_by_cruise_id($cruiseId), true);
// // print_r($zones);

// วนลูปตั้งแต่วันที่ 1 ถึงวันสุดท้ายของเดือน
$current_date = $first_day_of_month;
while ($current_date <= $last_day_of_month) {
    $p_roof = rand(1, 100);
    $p_floor1 = rand(1, 100);
    $p_floor2 = rand(1, 100);
    $p_floor3 = rand(1, 100);

    $data[] = [
        "Date" => date('d/m/Y', strtotime($current_date)),
        "Weekday" => date('l', strtotime($current_date)),
        "DayofWeek" => date('N', strtotime($current_date)),
        "Rooftop" => [
            "Percentage" => $p_roof,
            "Value" => $p_roof,
            "maxValue" => "100",
            "BarColor" => "bg-primary"
        ],
        "Floor1" => [
            "Percentage" => $p_floor1,
            "Value" => $p_floor1,
            "maxValue" => "100",
            "BarColor" => "bg-success"
        ],
        "Floor2" => [
            "Percentage" => $p_floor2,
            "Value" => $p_floor2,
            "maxValue" => "100",
            "BarColor" => "bg-danger"
        ],
        "Floor3" => [
            "Percentage" => $p_floor3,
            "Value" => $p_floor3,
            "maxValue" => "100",
            "BarColor" => "bg-info"
        ]
    ];
    $current_date = date('Y-m-d', strtotime($current_date . ' +1 day'));
}



// $data = [
//     [
//         "Date" => "01/03/2025",
//         "Rooftop" => [
//             "Percentage" => "10",
//             "Value" => "10",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "20",
//             "Value" => "20",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "30",
//             "Value" => "30",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "40",
//             "Value" => "40",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "02/03/2025",
//         "Rooftop" => [
//             "Percentage" => "20",
//             "Value" => "20",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "40",
//             "Value" => "40",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "70",
//             "Value" => "70",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "03/03/2025",
//         "Rooftop" => [
//             "Percentage" => "30",
//             "Value" => "30",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "50",
//             "Value" => "50",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "80",
//             "Value" => "80",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "04/03/2025",
//         "Rooftop" => [
//             "Percentage" => "40",
//             "Value" => "40",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "60",
//             "Value" => "60",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "90",
//             "Value" => "90",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "99",
//             "Value" => "99",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "05/03/2025",
//         "Rooftop" => [
//             "Percentage" => "50",
//             "Value" => "50",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "70",
//             "Value" => "70",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "06/03/2025",
//         "Rooftop" => [
//             "Percentage" => "60",
//             "Value" => "60",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "80",
//             "Value" => "80",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "07/03/2025",
//         "Rooftop" => [
//             "Percentage" => "70",
//             "Value" => "70",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "90",
//             "Value" => "90",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ],
//     [
//         "Date" => "08/03/2025",
//         "Rooftop" => [
//             "Percentage" => "80",
//             "Value" => "80",
//             "maxValue" => "100",
//             "BarColor" => "bg-primary"
//         ],
//         "Floor1" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-success"
//         ],
//         "Floor2" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-danger"
//         ],
//         "Floor3" => [
//             "Percentage" => "100",
//             "Value" => "100",
//             "maxValue" => "100",
//             "BarColor" => "bg-info"
//         ]
//     ]
// ];

$json_data = json_encode($data, JSON_PRETTY_PRINT);

$file = 'json_by_month.json';
file_put_contents($file, $json_data);

echo "ไฟล์ JSON ถูกสร้างสำเร็จ: " . $file;

?>