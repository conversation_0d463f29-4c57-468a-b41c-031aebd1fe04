<?php
// Set proper error reporting but don't display errors to users
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Start session
session_start();

// Include required files
include_once '../dbconnect/_dbconnect.php';
include_once 'csrf_token.php';
include_once 'rate_limit.php';

// Generate a nonce for inline scripts
$script_nonce = base64_encode(random_bytes(16));

// Set content type to JSON
header('Content-Type: application/json');

// Set security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY'); // This can only be set via HTTP header
header('X-XSS-Protection: 1; mode=block');
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'nonce-{$script_nonce}' https://cdn.jsdelivr.net https://code.jquery.com https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:;");
header('Referrer-Policy: strict-origin-when-cross-origin');

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Function to log login attempts
function log_login_attempt($username, $success, $ip) {
    $log_file = 'login_attempts.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] IP: $ip, Username: $username, Status: " . ($success ? 'Success' : 'Failed') . "\n";
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get client IP for logging
    $client_ip = $_SERVER['REMOTE_ADDR'];

    // Check CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        error_log("CSRF token validation failed");
        echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
        exit;
    }

    // For debugging
    error_log("CSRF token validated successfully");

    // Check rate limiting
    if (!check_rate_limit()) {
        $remaining_time = get_rate_limit_remaining_time();
        $minutes = ceil($remaining_time / 60);
        echo json_encode([
            'status' => 'rate_limited',
            'message' => "Too many failed attempts. Please try again in $minutes minutes.",
            'remaining' => $remaining_time
        ]);
        exit;
    }

    // Check login credentials
    if (isset($_POST['username']) && isset($_POST['password'])) {
        // Sanitize inputs
        $username = sanitize_input($_POST['username']);
        $password = sanitize_input($_POST['password']);

        // Basic validation
        if (empty($username) || empty($password)) {
            increment_login_attempts();
            log_login_attempt($username, false, $client_ip);
            echo json_encode(['status' => 'error', 'message' => 'Username and password are required']);
            exit;
        }

        // Prevent brute force attacks by adding a small delay
        usleep(200000); // 0.2 seconds delay

        // Enable error logging for debugging
        error_log("Login attempt for user: {$username}");

        // Check login
        $login_result = check_login($username, $password);

        // Log the result for debugging
        error_log("Login result: {$login_result}");

        if ($login_result == "success") {
            // Reset login attempts on successful login
            reset_login_attempts();

            // Session regeneration is now handled in the check_login function

            // Set session timeout (30 minutes)
            // This is now set in the check_login function, but we'll keep it here for redundancy
            $_SESSION['last_activity'] = time();
            $_SESSION['expire_time'] = 30 * 60; // 30 minutes

            // Log successful login
            log_login_attempt($username, true, $client_ip);

            echo json_encode(['status' => 'success']);
        } elseif ($login_result == "first_login_required") {
            // Reset login attempts on successful login
            reset_login_attempts();

            // Set session timeout (30 minutes)
            $_SESSION['last_activity'] = time();
            $_SESSION['expire_time'] = 30 * 60; // 30 minutes

            // Log successful login
            log_login_attempt($username, true, $client_ip);

            echo json_encode(['status' => 'first_login_required']);
        } else {
            // Increment failed login attempts
            increment_login_attempts();

            // Log failed login
            log_login_attempt($username, false, $client_ip);

            // Return appropriate error message based on the login result
            $error_message = 'Invalid username or password';

            if ($login_result == "Account Inactive") {
                $error_message = 'Your account is inactive. Please contact an administrator.';
            } else if ($login_result == "System Error") {
                $error_message = 'A system error occurred. Please try again later.';
            }

            echo json_encode(['status' => 'error', 'message' => $error_message]);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Username and password are required']);
    }
} else {
    // Not a POST request
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
}
?>