/**
 * Load Agents Script
 * Fetches agents from the database and populates the agent select boxes
 */

$(document).ready(function() {
    // Load agents when the document is ready
    loadAgents();
    
    // Reload agents when modals are shown
    $('#bookingModal, #buyoutFloorModal').on('shown.bs.modal', function() {
        loadAgents();
    });
});

/**
 * Load agents from the database
 */
function loadAgents() {
    console.log('Loading agents from database...');
    
    // Show loading state
    $('#agent, #buyout-agent').html('<option value="">Loading agents...</option>');
    
    // Fetch agents from the server
    $.ajax({
        url: 'get_agents.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && Array.isArray(response.data)) {
                // Clear existing options
                $('#agent, #buyout-agent').empty();
                
                // Add default option
                $('#agent, #buyout-agent').append('<option value="">Select Agent</option>');
                
                // Add agents from database
                response.data.forEach(function(agent) {
                    $('#agent, #buyout-agent').append(`<option value="${agent.name}">${agent.name}</option>`);
                });
                
                console.log(`Loaded ${response.data.length} agents from database`);
            } else {
                console.error('Failed to load agents:', response.message || 'Unknown error');
                
                // Add default options if loading fails
                $('#agent, #buyout-agent').html('<option value="">Select Agent</option>');
                
                // Add some default agents as fallback
                const defaultAgents = ['Agent 1', 'Agent 2', 'Agent 3', 'Agent 4', 'Agent 5'];
                defaultAgents.forEach(function(agent) {
                    $('#agent, #buyout-agent').append(`<option value="${agent}">${agent}</option>`);
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading agents:', error);
            
            // Add default options if loading fails
            $('#agent, #buyout-agent').html('<option value="">Select Agent</option>');
            
            // Add some default agents as fallback
            const defaultAgents = ['Agent 1', 'Agent 2', 'Agent 3', 'Agent 4', 'Agent 5'];
            defaultAgents.forEach(function(agent) {
                $('#agent, #buyout-agent').append(`<option value="${agent}">${agent}</option>`);
            });
        }
    });
}
