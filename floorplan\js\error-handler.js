/**
 * <PERSON><PERSON><PERSON>
 * This script handles common JavaScript errors that might occur in the floorplan system
 * and provides placeholder functions for functions that might be loaded later
 */

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Error handler script loaded');
    // Only set up the error handler if it doesn't already exist
    if (!window.errorHandlerInitialized) {
        window.errorHandlerInitialized = true;

        // Create a global error handler to catch and handle errors
        window.onerror = function(message, source, lineno, colno, error) {
        // Ignore errors from third-party scripts or browser extensions
        if (source && (
            source.includes('chrome-extension://') ||
            message.includes('cookieData') ||
            message.includes('innerHTML') ||
            message.includes('Identifier') ||
            message.includes('has already been declared')
        )) {
            // Prevent the error from being reported to the console
            console.log('Suppressed error:', message);
            return true; // Prevents the error from being shown in the console
        }

        // Handle specific errors with more detailed logging
        if (message.includes('formatDateForAPI') || message.includes('loadBookedTables')) {
            console.log('Handled error:', message);
            console.log('Error source:', source);
            console.log('Line:', lineno, 'Column:', colno);

            // Try to fix the error by ensuring the functions exist
            if (message.includes('formatDateForAPI') && typeof window.formatDateForAPI !== 'function') {
                console.log('Fixing missing formatDateForAPI function');
                // Define the function if it doesn't exist
                window.formatDateForAPI = function(dateString) {
                    try {
                        // Try to convert dd-mm-yyyy to yyyy-mm-dd
                        if (dateString && dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                            const parts = dateString.split('-');
                            return `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                        // Return today's date as a fallback
                        const today = new Date();
                        return today.toISOString().split('T')[0];
                    } catch (e) {
                        console.error('Error in fallback formatDateForAPI:', e);
                        // Return today's date as a fallback
                        const today = new Date();
                        return today.toISOString().split('T')[0];
                    }
                };
            }

            if (message.includes('loadBookedTables') && typeof window.loadBookedTables !== 'function') {
                console.log('Fixing missing loadBookedTables function');
                // Define the function if it doesn't exist
                window.loadBookedTables = function(date, zone) {
                    console.log('Fallback loadBookedTables called with:', date, zone);
                };
            }

            return true; // Prevent the error from being shown in the console
        }

        // Let other errors propagate normally
        return false;
    };
    }

    // Only define placeholder functions if they don't already exist
    console.log('Checking for missing functions...');

    // Define empty variables to prevent errors
    if (typeof cookieData === 'undefined') {
        window.cookieData = {};
    }

    // Define a dummy innerHTML function if needed
    if (typeof innerHTML === 'undefined') {
        window.innerHTML = function() {};
    }

    // Placeholder for markBookedTables
    if (typeof window.markBookedTables !== 'function') {
        window.markBookedTables = function(bookedTables = []) {
            console.log('Placeholder markBookedTables called with:', bookedTables);
            // This is just a placeholder that will be replaced by the actual function
            try {
                // Ensure bookedTables is an array
                if (!Array.isArray(bookedTables)) {
                    console.error('bookedTables is not an array:', bookedTables);
                    bookedTables = [];
                }

                console.log('Placeholder markBookedTables: Would mark', bookedTables.length, 'tables as booked');
            } catch (e) {
                console.error('Error in placeholder markBookedTables:', e);
            }
        };
    }

    // Placeholder for formatDateForAPI - only define if not already defined
    if (typeof window.formatDateForAPI !== 'function') {
        window.formatDateForAPI = function(dateString) {
            console.log('Placeholder formatDateForAPI called with:', dateString);
            // Try to format the date in a basic way
            try {
                // Handle null or undefined
                if (!dateString) {
                    const today = new Date();
                    return today.toISOString().split('T')[0]; // YYYY-MM-DD
                }

                // Check if already in yyyy-mm-dd format
                if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    return dateString; // Already in correct format
                }

                // Check if the date is in dd-mm-yyyy format
                if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    // Convert from dd-mm-yyyy to yyyy-mm-dd
                    const parts = dateString.split('-');
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }

                // Check if the date is in dd/mm/yyyy format
                if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                    // Convert from dd/mm/yyyy to yyyy-mm-dd
                    const parts = dateString.split('/');
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }

                // If it's not in a recognized format, try to parse it as a date
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    console.error('Invalid date for API:', dateString);
                    // Return today's date as a fallback
                    const today = new Date();
                    return today.toISOString().split('T')[0]; // YYYY-MM-DD
                }
                return date.toISOString().split('T')[0]; // YYYY-MM-DD
            } catch (e) {
                console.error('Error in placeholder formatDateForAPI:', e);
                // Return today's date as a fallback
                const today = new Date();
                return today.toISOString().split('T')[0]; // YYYY-MM-DD
            }
        };
    }

    // Placeholder for updateFloorOccupancy
    if (typeof window.updateFloorOccupancy !== 'function') {
        window.updateFloorOccupancy = function(floorNumber, bookedCount) {
            console.log('Placeholder updateFloorOccupancy called with:', floorNumber, bookedCount);
            // This is just a placeholder that will be replaced by the actual function
            try {
                // Ensure floorNumber is a valid number
                let safeFloorNumber = parseInt(floorNumber) || 1;
                if (isNaN(safeFloorNumber) || safeFloorNumber < 1 || safeFloorNumber > 3) {
                    safeFloorNumber = 1;
                }

                // Ensure bookedCount is a valid number
                let safeBookedCount = parseInt(bookedCount) || 0;
                if (isNaN(safeBookedCount) || safeBookedCount < 0) {
                    safeBookedCount = 0;
                }

                console.log('Placeholder updateFloorOccupancy: Would update floor', safeFloorNumber, 'with booked count', safeBookedCount);
            } catch (e) {
                console.error('Error in placeholder updateFloorOccupancy:', e);
            }
        };
    }

    // Placeholder for markDisabledTables
    if (typeof window.markDisabledTables !== 'function') {
        window.markDisabledTables = function(disabledTables = [], zone) {
            console.log('Placeholder markDisabledTables called with:', disabledTables, zone);
            // This is just a placeholder that will be replaced by the actual function
            try {
                // Ensure disabledTables is an array
                if (!Array.isArray(disabledTables)) {
                    console.error('disabledTables is not an array:', disabledTables);
                    disabledTables = [];
                }

                // Ensure zone is a valid number
                let safeZone = parseInt(zone) || 1;
                if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                    safeZone = 1;
                }

                console.log('Placeholder markDisabledTables: Would mark', disabledTables.length, 'tables as disabled for zone', safeZone);
            } catch (e) {
                console.error('Error in placeholder markDisabledTables:', e);
            }
        };
    }

    // Placeholder for loadDisabledTables
    if (typeof window.loadDisabledTables !== 'function') {
        window.loadDisabledTables = function(zone) {
            console.log('Placeholder loadDisabledTables called with:', zone);
            // This is just a placeholder that will be replaced by the actual function
            try {
                // Ensure zone is a valid number
                let safeZone = parseInt(zone) || 1;
                if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                    safeZone = 1;
                }

                console.log('Placeholder loadDisabledTables: Would load disabled tables for zone', safeZone);

                // Make the AJAX call
                $.ajax({
                    url: 'get_disabled_tables.php',
                    type: 'GET',
                    data: { useZone: safeZone },
                    dataType: 'json',
                    success: function(disabled) {
                        console.log('Placeholder loadDisabledTables got data:', disabled);
                        // Try to use the real markDisabledTables function if it exists
                        if (typeof window.markDisabledTables === 'function') {
                            window.markDisabledTables(disabled, safeZone);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error in placeholder loadDisabledTables:', error);
                        console.error('Response text:', xhr.responseText);
                    }
                });
            } catch (e) {
                console.error('Error in placeholder loadDisabledTables:', e);
            }
        };
    }

    // Placeholder for loadBookedTables
    if (typeof window.loadBookedTables !== 'function') {
        window.loadBookedTables = function(date, zone) {
            console.log('Placeholder loadBookedTables called with:', date, zone);
            // This is just a placeholder that will be replaced by the actual function
            // We'll try to load the data using a direct AJAX call
            try {
                // Ensure date is properly formatted
                let formattedDate = date;
                if (typeof window.formatDateForAPI === 'function') {
                    formattedDate = window.formatDateForAPI(date);
                }

                // Ensure zone is a valid number
                let safeZone = parseInt(zone) || 1;
                if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                    safeZone = 1;
                }

                // markBookedTables is defined at the top level of this file

                // loadDisabledTables is defined at the top level of this file

                // updateFloorOccupancy is defined at the top level of this file

                // Make the AJAX call
                $.ajax({
                    url: 'get_booked_tables.php',
                    type: 'GET',
                    data: { useDate: formattedDate, useZone: safeZone },
                    dataType: 'json',
                    success: function(booked) {
                        try {
                            console.log('Placeholder loadBookedTables got data:', booked);

                            // Ensure booked is an array
                            if (!Array.isArray(booked)) {
                                console.warn('Booked tables response is not an array:', booked);
                                booked = [];
                            }

                            // Call markBookedTables
                            window.markBookedTables(booked);

                            // Call loadDisabledTables
                            window.loadDisabledTables(safeZone);

                            // Call updateFloorOccupancy
                            window.updateFloorOccupancy(safeZone, booked.length);
                        } catch (e) {
                            console.error('Error processing booked tables response:', e);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error in placeholder loadBookedTables:', error);
                        console.error('Response text:', xhr.responseText);
                    }
                });
            } catch (e) {
                console.error('Error in placeholder loadBookedTables:', e);
            }
        };
    }
});
