-- Update special_request column to INT type for consistency
-- This script is safe to run multiple times

-- Check if the column exists and update its type
SET @exists_special_request = 0;
SELECT COUNT(*) INTO @exists_special_request
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'special_request';

-- Update column type if it exists
SET @sql_update_special_request = IF(
    @exists_special_request > 0,
    'ALTER TABLE kp_booking MODIFY COLUMN special_request INT DEFAULT 0 COMMENT "0=None, 1=Birthday, 2=Anniversary"',
    'SELECT "Column special_request does not exist" as message'
);

PREPARE stmt FROM @sql_update_special_request;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update any existing TEXT values to INT values
-- Convert empty strings and NULL to 0
UPDATE kp_booking 
SET special_request = 0 
WHERE special_request IS NULL 
   OR special_request = '' 
   OR special_request = 'None'
   OR special_request = '0';

-- Convert birthday text to 1
UPDATE kp_booking 
SET special_request = 1 
WHERE special_request LIKE '%birthday%' 
   OR special_request LIKE '%Birthday%'
   OR special_request = '1';

-- Convert anniversary text to 2
UPDATE kp_booking 
SET special_request = 2 
WHERE special_request LIKE '%anniversary%' 
   OR special_request LIKE '%Anniversary%'
   OR special_request = '2';

SELECT 'Special request column updated successfully' as message;
