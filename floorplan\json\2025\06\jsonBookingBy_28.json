[{"Order No": "SWD2025060006", "BookID": 59, "ZoneID": 2, "Use Date": "2025-06-28", "Amount": "1000", "Create Date": "2025-06-28 11:53:14", "Customer": "KoPiKo", "Phone": "029009000", "Guests": {"Adults": 100, "Children": 20, "Infants": 5}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "7Day", "Request": "", "Special": 0, "Floor": "2", "Table": "B26,B25"}, {"Order No": "SWD2025060007", "BookID": 60, "ZoneID": 2, "Use Date": "2025-06-28", "Amount": "1000", "Create Date": "2025-06-28 11:57:03", "Customer": "KoPiKo", "Phone": "029009000", "Guests": {"Adults": 100, "Children": 50, "Infants": 5}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "7Day", "Request": "", "Special": 0, "Floor": "2", "Table": "B12,B14,B15,B16,B17,B18,B19,B20,B21,B22,B23,B24"}, {"Order No": "SWD2025060008", "BookID": 62, "ZoneID": 2, "Use Date": "2025-06-28", "Amount": "1000", "Create Date": "2025-06-28 12:30:37", "Customer": "KoPiKo", "Phone": "029009000", "Guests": {"Adults": 100, "Children": 50, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Request": "", "Special": 0, "Floor": "2", "Table": "H7,H7-1,H7-2"}, {"Order No": "SWD2025060009", "BookID": 63, "ZoneID": 3, "Use Date": "2025-06-28", "Amount": "1000", "Create Date": "2025-06-28 13:51:33", "Customer": "<PERSON><PERSON><PERSON>", "Phone": "029009000", "Guests": {"Adults": 10, "Children": 5, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "7Day", "Request": "", "Special": 0, "Floor": "3", "Table": "A36-2"}, {"Order No": "SWD2025060010", "BookID": 64, "ZoneID": 3, "Use Date": "2025-06-28", "Amount": "1000", "Create Date": "2025-06-28 13:52:00", "Customer": "KoPiKo", "Phone": "029009000", "Guests": {"Adults": 100, "Children": 5, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "", "Request": "", "Special": 0, "Floor": "3", "Table": "A19,A20,A21"}, {"Order No": "SWD2025060011", "BookID": 65, "ZoneID": 1, "Use Date": "2025-06-28", "Amount": "500", "Create Date": "2025-06-28 13:53:28", "Customer": "KoPiKO", "Phone": "02900900", "Guests": {"Adults": 100, "Children": 5, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "", "Request": "", "Special": 0, "Floor": "1", "Table": "C12,C14,C15"}, {"Order No": "SWD2025060012", "BookID": 66, "ZoneID": 1, "Use Date": "2025-06-28", "Amount": "100", "Create Date": "2025-06-28 16:04:16", "Customer": "MrKP", "Phone": "02900", "Guests": {"Adults": 10, "Children": 5, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "", "Request": "", "Special": 0, "Floor": "1", "Table": "C12-2,C12-3"}, {"Order No": "SWD2025060013", "BookID": 67, "ZoneID": 2, "Use Date": "2025-06-28", "Amount": "100", "Create Date": "2025-06-28 16:06:02", "Customer": "MrKP", "Phone": "0865395090", "Guests": {"Adults": 10, "Children": 5, "Infants": 0}, "Guide": "0", "FOC": "0", "TL": "0", "Voucher No": "", "Agent": "7Day", "Request": "Brithday Cake", "Special": 0, "Floor": "2", "Table": "A36,A35,H12,H12-1,H12-2"}]