<?php
/**
 * User Management Main Page
 * 
 * This page provides an interface for managing users
 */
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/utils/Session.php';
require_once dirname(__DIR__) . '/utils/Security.php';
require_once dirname(__DIR__) . '/models/User.php';

// Start session
Session::start();

// Set security headers
Security::setSecurityHeaders();

// Check if user is logged in
if (!Session::isLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// Check if user has admin role
if (Session::get('role') !== 'admin') {
    header('Location: ../index.php?error=unauthorized');
    exit;
}

// Generate CSRF token
$csrf_token = Session::generateCsrfToken();

// Create User model instance
$userModel = new User();

// Get all users
$users = $userModel->getAllUsers();

// Include header
$pageTitle = "User Management";
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">User Management</h4>
            <div class="card-actions">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="ti ti-plus"></i> Add New User
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    $message = '';
                    switch ($_GET['success']) {
                        case 'add':
                            $message = 'User added successfully.';
                            break;
                        case 'edit':
                            $message = 'User updated successfully.';
                            break;
                        case 'delete':
                            $message = 'User deleted successfully.';
                            break;
                        default:
                            $message = 'Operation completed successfully.';
                    }
                    echo $message;
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    $message = '';
                    switch ($_GET['error']) {
                        case 'add':
                            $message = 'Failed to add user.';
                            break;
                        case 'edit':
                            $message = 'Failed to update user.';
                            break;
                        case 'delete':
                            $message = 'Failed to delete user.';
                            break;
                        case 'unauthorized':
                            $message = 'You do not have permission to perform this action.';
                            break;
                        default:
                            $message = 'An error occurred.';
                    }
                    echo $message;
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table id="userTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($users): ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo $user['user']; ?></td>
                                    <td><?php echo $user['name']; ?></td>
                                    <td><?php echo $user['email']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $user['role'] === 'admin' ? 'danger' : 
                                                ($user['role'] === 'manager' ? 'warning' : 'primary'); 
                                        ?>">
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $user['status'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($user['create_date'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info edit-user" 
                                                data-id="<?php echo $user['id']; ?>"
                                                data-username="<?php echo $user['user']; ?>"
                                                data-name="<?php echo $user['name']; ?>"
                                                data-email="<?php echo $user['email']; ?>"
                                                data-role="<?php echo $user['role']; ?>"
                                                data-status="<?php echo $user['status']; ?>"
                                                data-bs-toggle="modal" data-bs-target="#editUserModal">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <?php if ($user['id'] != Session::get('id')): // Prevent self-deletion ?>
                                                <button type="button" class="btn btn-sm btn-danger delete-user" 
                                                    data-id="<?php echo $user['id']; ?>"
                                                    data-username="<?php echo $user['user']; ?>">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">No users found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="addUserForm" action="user_actions.php" method="post">
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="invalid-feedback">Please enter a username.</div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="invalid-feedback">Please enter a password.</div>
                        
                        <!-- Password strength indicator -->
                        <div class="password-strength mt-2">
                            <div class="password-strength-bar" id="passwordStrengthBar"></div>
                        </div>
                        <div class="password-feedback" id="passwordFeedback"></div>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="invalid-feedback">Please enter a name.</div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback">Please enter a valid email.</div>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user">User</option>
                            <option value="manager">Manager</option>
                            <option value="admin">Admin</option>
                        </select>
                        <div class="invalid-feedback">Please select a role.</div>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                        <div class="invalid-feedback">Please select a status.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="editUserForm" action="user_actions.php" method="post">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="user_id" id="edit_user_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required readonly>
                        <div class="invalid-feedback">Please enter a username.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                        
                        <!-- Password strength indicator -->
                        <div class="password-strength mt-2">
                            <div class="password-strength-bar" id="editPasswordStrengthBar"></div>
                        </div>
                        <div class="password-feedback" id="editPasswordFeedback"></div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                        <div class="invalid-feedback">Please enter a name.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                        <div class="invalid-feedback">Please enter a valid email.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="user">User</option>
                            <option value="manager">Manager</option>
                            <option value="admin">Admin</option>
                        </select>
                        <div class="invalid-feedback">Please select a role.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                        <div class="invalid-feedback">Please select a status.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="deleteUserForm" action="user_actions.php" method="post">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="user_id" id="delete_user_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteUserModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the user <strong id="delete_username"></strong>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .password-strength {
        height: 5px;
        width: 100%;
        background: #ddd;
        border-radius: 3px;
    }
    .password-strength-bar {
        height: 100%;
        border-radius: 3px;
        transition: width 0.3s ease;
        width: 0%;
    }
    .weak { width: 25%; background-color: #ff4d4d; }
    .medium { width: 50%; background-color: #ffaa00; }
    .strong { width: 75%; background-color: #73e600; }
    .very-strong { width: 100%; background-color: #00b33c; }
    .password-feedback {
        font-size: 12px;
        margin-top: 5px;
        color: #666;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    if (typeof $.fn.DataTable !== 'undefined') {
        $('#userTable').DataTable({
            responsive: true,
            order: [[0, 'asc']]
        });
    }

    // Password strength checker for Add User form
    $("#password").on("input", function() {
        checkPasswordStrength($(this).val(), "#passwordStrengthBar", "#passwordFeedback");
    });

    // Password strength checker for Edit User form
    $("#edit_password").on("input", function() {
        checkPasswordStrength($(this).val(), "#editPasswordStrengthBar", "#editPasswordFeedback");
    });

    // Function to check password strength
    function checkPasswordStrength(password, strengthBarSelector, feedbackSelector) {
        var strength = 0;
        var feedback = "";

        if (password.length > 0) {
            // Check password length
            if (password.length >= 8) {
                strength += 1;
            } else {
                feedback += "Password should be at least 8 characters. ";
            }

            // Check for uppercase letters
            if (/[A-Z]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add uppercase letters. ";
            }

            // Check for lowercase letters
            if (/[a-z]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add lowercase letters. ";
            }

            // Check for numbers
            if (/[0-9]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add numbers. ";
            }

            // Check for special characters
            if (/[^A-Za-z0-9]/.test(password)) {
                strength += 1;
            } else {
                feedback += "Add special characters. ";
            }

            // Update strength bar
            var $strengthBar = $(strengthBarSelector);
            $strengthBar.removeClass("weak medium strong very-strong");

            if (strength === 0) {
                $strengthBar.css("width", "0%");
            } else if (strength <= 2) {
                $strengthBar.addClass("weak");
            } else if (strength <= 3) {
                $strengthBar.addClass("medium");
            } else if (strength <= 4) {
                $strengthBar.addClass("strong");
            } else {
                $strengthBar.addClass("very-strong");
            }

            // Update feedback
            $(feedbackSelector).text(feedback);
        } else {
            $(strengthBarSelector).css("width", "0%").removeClass("weak medium strong very-strong");
            $(feedbackSelector).text("");
        }
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Edit user modal
    $('.edit-user').click(function() {
        const userId = $(this).data('id');
        const username = $(this).data('username');
        const name = $(this).data('name');
        const email = $(this).data('email');
        const role = $(this).data('role');
        const status = $(this).data('status');

        $('#edit_user_id').val(userId);
        $('#edit_username').val(username);
        $('#edit_name').val(name);
        $('#edit_email').val(email);
        $('#edit_role').val(role);
        $('#edit_status').val(status);
        
        // Reset password field and strength indicator
        $('#edit_password').val('');
        $('#editPasswordStrengthBar').css("width", "0%").removeClass("weak medium strong very-strong");
        $('#editPasswordFeedback').text("");
    });

    // Delete user confirmation
    $('.delete-user').click(function() {
        const userId = $(this).data('id');
        const username = $(this).data('username');
        
        $('#delete_user_id').val(userId);
        $('#delete_username').text(username);
        
        $('#deleteUserModal').modal('show');
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
