<?php
// Add first_login field to kp_login table
require_once '../dbconnect/_dbconnect.php';

echo "<h2>Adding first_login field to kp_login table</h2>";

try {
    // Get database connection
    $pdo = db_connect();
    
    // Check if first_login column exists
    $checkSql = "SELECT COUNT(*) as count FROM information_schema.columns 
                 WHERE table_schema = DATABASE() 
                 AND table_name = 'kp_login' 
                 AND column_name = 'first_login'";
    
    $stmt = $pdo->query($checkSql);
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "<p>Adding first_login column...</p>";
        
        // Add the first_login column
        $alterSql = "ALTER TABLE kp_login 
                     ADD COLUMN first_login TINYINT(1) NOT NULL DEFAULT 0 
                     COMMENT '1 = User must change password on first login, 0 = Normal login' 
                     AFTER status";
        
        $pdo->exec($alterSql);
        echo "<p style='color: green;'>✅ first_login column added successfully!</p>";
        
        // Update existing users to not require password change
        $updateSql = "UPDATE kp_login SET first_login = 0";
        $pdo->exec($updateSql);
        echo "<p style='color: green;'>✅ Existing users updated to not require password change</p>";
        
    } else {
        echo "<p style='color: blue;'>ℹ️ first_login column already exists</p>";
    }
    
    // Show current table structure
    echo "<h3>Current kp_login table structure:</h3>";
    $descSql = "DESCRIBE kp_login";
    $stmt = $pdo->query($descSql);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green;'>✅ Database update completed successfully!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
